import request from '@/utils/request'

// 生成指定日期的日报数据
export function generateDailyReport(reportDate) {
  return request({
    url: `/worker/dailyReport/generate/${reportDate}`,
    method: 'post'
  })
}

// 查询人员维度日报统计
export function getUserDailyReport(data) {
  return request({
    url: '/worker/dailyReport/user',
    method: 'post',
    data: data
  })
}

// 查询部门维度日报统计
export function getDeptDailyReport(data) {
  return request({
    url: '/worker/dailyReport/dept',
    method: 'post',
    data: data
  })
}

// 查询项目维度日报统计
export function getProjectDailyReport(data) {
  return request({
    url: '/worker/dailyReport/project',
    method: 'post',
    data: data
  })
}

// 查询日报详细数据
export function getDailyReportDetails(data) {
  return request({
    url: '/worker/dailyReport/details',
    method: 'post',
    data: data
  })
}

// 导出日报统计数据
export function exportDailyReport(data) {
  return request({
    url: '/worker/dailyReport/export',
    method: 'post',
    data: data
  })
}
