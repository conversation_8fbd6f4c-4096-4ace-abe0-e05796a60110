import request from '@/utils/request'

// 查询绩效系数配置列表
export function listPerformanceCoefficient(query) {
  return request({
    url: '/worker/coefficient/list',
    method: 'get',
    params: query
  })
}

// 查询绩效系数配置详细
export function getPerformanceCoefficient(coefficientId) {
  return request({
    url: '/worker/coefficient/' + coefficientId,
    method: 'get'
  })
}

// 新增绩效系数配置
export function addPerformanceCoefficient(data) {
  return request({
    url: '/worker/coefficient',
    method: 'post',
    data: data
  })
}

// 修改绩效系数配置
export function updatePerformanceCoefficient(data) {
  return request({
    url: '/worker/coefficient',
    method: 'put',
    data: data
  })
}

// 删除绩效系数配置
export function delPerformanceCoefficient(coefficientIds) {
  return request({
    url: '/worker/coefficient/' + coefficientIds,
    method: 'delete'
  })
}

// 根据项目ID获取绩效系数
export function getCoefficientByProjectId(projectId) {
  return request({
    url: '/worker/coefficient/getByProject/' + projectId,
    method: 'get'
  })
}
