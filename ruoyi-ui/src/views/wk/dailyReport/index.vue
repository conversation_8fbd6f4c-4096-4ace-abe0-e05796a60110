<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="日期范围" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          @change="handleDateChange"
        />
      </el-form-item>
      <el-form-item label="统计维度" prop="reportType">
        <el-select v-model="queryParams.reportType" placeholder="请选择统计维度" @change="handleReportTypeChange">
          <el-option label="人员维度" value="1" />
          <el-option label="部门维度" value="2" />
          <el-option label="项目维度" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="姓名" prop="userName" v-if="queryParams.reportType === '1'">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="部门" prop="deptName" v-if="queryParams.reportType === '2'">
        <el-input
          v-model="queryParams.deptName"
          placeholder="请输入部门名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleGenerate"
          v-hasPermi="['worker:dailyReport:generate']"
        >生成日报</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-view"
          size="mini"
          @click="handleViewDetails"
          v-hasPermi="['worker:dailyReport:detail']"
        >查看详情</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['worker:dailyReport:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 人员维度表格 -->
    <el-table v-if="queryParams.reportType === '1'" v-loading="loading" :data="reportList" border>
      <el-table-column label="日期" align="center" prop="reportDate" width="100" />
      <el-table-column label="姓名" align="center" prop="userName" width="100" />
      <el-table-column label="项目" align="center" prop="projectName" width="120" />
      <el-table-column label="拍摄调研时间" align="center" prop="surveyStartTime" width="120" />
      <el-table-column label="结束调研时间" align="center" prop="surveyEndTime" width="120" />
      <el-table-column label="有效时长(小时)" align="center" width="110">
        <template slot-scope="scope">
          {{ formatHours(scope.row.effectiveHours) }}
        </template>
      </el-table-column>
      <el-table-column label="非有效时长(小时)" align="center" width="120">
        <template slot-scope="scope">
          {{ formatHours(scope.row.ineffectiveHours) }}
        </template>
      </el-table-column>
      <el-table-column label="工作时长(小时)" align="center" width="110">
        <template slot-scope="scope">
          {{ formatHours(scope.row.workHours) }}
        </template>
      </el-table-column>
      <el-table-column label="工单间隔时间(小时)" align="center" width="140">
        <template slot-scope="scope">
          {{ formatHours(scope.row.avgIntervalMinutes) }}
        </template>
      </el-table-column>
      <el-table-column label="不合格指标数量" align="center" prop="unqualifiedCount" width="120" />
      <el-table-column label="合格指标数量" align="center" prop="qualifiedCount" width="110" />
      <el-table-column label="指标合计" align="center" prop="totalIndicators" width="100" />
      <el-table-column label="工单数" align="center" prop="workOrderCount" width="80" />
      <el-table-column label="绩效点" align="center" prop="performancePoints" width="80" />
      <el-table-column label="有效公里数" align="center" prop="effectiveKilometers" width="100" />
      <el-table-column label="非有效公里数" align="center" prop="ineffectiveKilometers" width="110" />
      <el-table-column label="公里数" align="center" prop="totalKilometers" width="80" />
    </el-table>

    <!-- 部门维度表格 -->
    <el-table v-if="queryParams.reportType === '2'" v-loading="loading" :data="reportList" border>
      <el-table-column label="日期" align="center" prop="reportDate" width="100" />
      <el-table-column label="部门" align="center" prop="deptName" width="120" />
      <el-table-column label="项目" align="center" prop="projectName" width="120" />
      <el-table-column label="有效时长(小时)" align="center" width="110">
        <template slot-scope="scope">
          {{ formatHours(scope.row.effectiveHours) }}
        </template>
      </el-table-column>
      <el-table-column label="非有效时长(小时)" align="center" width="120">
        <template slot-scope="scope">
          {{ formatHours(scope.row.ineffectiveHours) }}
        </template>
      </el-table-column>
      <el-table-column label="工作时长(小时)" align="center" width="110">
        <template slot-scope="scope">
          {{ formatHours(scope.row.workHours) }}
        </template>
      </el-table-column>
      <el-table-column label="工单间隔时间(小时)" align="center" width="140">
        <template slot-scope="scope">
          {{ formatHours(scope.row.avgIntervalMinutes) }}
        </template>
      </el-table-column>
      <el-table-column label="不合格指标数量" align="center" prop="unqualifiedCount" width="120" />
      <el-table-column label="合格指标数量" align="center" prop="qualifiedCount" width="110" />
      <el-table-column label="指标合计" align="center" prop="totalIndicators" width="100" />
      <el-table-column label="工单数" align="center" prop="workOrderCount" width="80" />
      <el-table-column label="绩效点" align="center" prop="performancePoints" width="80" />
      <el-table-column label="有效公里数" align="center" prop="effectiveKilometers" width="100" />
      <el-table-column label="非有效公里数" align="center" prop="ineffectiveKilometers" width="110" />
      <el-table-column label="公里数" align="center" prop="totalKilometers" width="80" />
    </el-table>

    <!-- 项目维度表格 -->
    <el-table v-if="queryParams.reportType === '3'" v-loading="loading" :data="reportList" border>
      <el-table-column label="日期" align="center" prop="reportDate" width="100" />
      <el-table-column label="项目" align="center" prop="projectName" width="120" />
      <el-table-column label="工单总数" align="center" prop="totalWorkOrders" width="100" />
      <el-table-column label="当日完成工单数" align="center" prop="completedWorkOrders" width="130" />
      <el-table-column label="累计完成工单数" align="center" prop="cumulativeCompletedWorkOrders" width="130" />
      <el-table-column label="待完成工单数" align="center" prop="pendingWorkOrders" width="110" />
      <el-table-column label="有效时长(小时)" align="center" width="110">
        <template slot-scope="scope">
          {{ formatHours(scope.row.effectiveHours) }}
        </template>
      </el-table-column>
      <el-table-column label="非有效时长(小时)" align="center" width="120">
        <template slot-scope="scope">
          {{ formatHours(scope.row.ineffectiveHours) }}
        </template>
      </el-table-column>
      <el-table-column label="不合格指标数量" align="center" prop="unqualifiedCount" width="120" />
      <el-table-column label="合格指标数量" align="center" prop="qualifiedCount" width="110" />
      <el-table-column label="指标合计" align="center" prop="totalIndicators" width="100" />
      <el-table-column label="绩效点" align="center" prop="performancePoints" width="80" />
      <el-table-column label="有效公里数" align="center" prop="effectiveKilometers" width="100" />
      <el-table-column label="非有效公里数" align="center" prop="ineffectiveKilometers" width="110" />
      <el-table-column label="公里数" align="center" prop="totalKilometers" width="80" />
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  generateDailyReport,
  getUserDailyReport,
  getDeptDailyReport,
  getProjectDailyReport,
  getDailyReportDetails,
  exportDailyReport
} from "@/api/worker/dailyReport";

export default {
  name: "DailyReport",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 日报统计表格数据
      reportList: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        reportType: '1',
        startDate: null,
        endDate: null,
        userName: null,
        deptName: null,
        projectName: null,
      }
    };
  },
  created() {
    // 默认查询最近7天的数据
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 7);

    this.dateRange = [
      this.formatDate(startDate),
      this.formatDate(endDate)
    ];
    this.handleDateChange(this.dateRange);
    this.getList();
  },
  methods: {
    /** 查询日报统计列表 */
    getList() {
      this.loading = true;
      let apiMethod;

      switch (this.queryParams.reportType) {
        case '1':
          apiMethod = getUserDailyReport;
          break;
        case '2':
          apiMethod = getDeptDailyReport;
          break;
        case '3':
          apiMethod = getProjectDailyReport;
          break;
        default:
          apiMethod = getUserDailyReport;
      }

      apiMethod(this.queryParams).then(response => {
        this.reportList = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 日期范围改变 */
    handleDateChange(dateRange) {
      if (dateRange && dateRange.length === 2) {
        this.queryParams.startDate = dateRange[0];
        this.queryParams.endDate = dateRange[1];
      } else {
        this.queryParams.startDate = null;
        this.queryParams.endDate = null;
      }
    },
    /** 统计维度改变 */
    handleReportTypeChange() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        reportType: '1',
        startDate: null,
        endDate: null,
        userName: null,
        deptName: null,
        projectName: null,
      };
      this.resetForm("queryForm");
      this.getList();
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('worker/dailyReport/export', {
        ...this.queryParams
      }, `daily_report_${new Date().getTime()}.xlsx`)
    },
    /** 生成日报数据 */
    handleGenerate() {
      this.$prompt('请输入要生成日报的日期', '生成日报', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^\d{4}-\d{2}-\d{2}$/,
        inputErrorMessage: '日期格式不正确，请输入YYYY-MM-DD格式',
        inputValue: this.formatDate(new Date())
      }).then(({ value }) => {
        this.loading = true;
        generateDailyReport(value).then(response => {
          this.$modal.msgSuccess("日报数据生成成功");
          this.getList();
        }).finally(() => {
          this.loading = false;
        });
      }).catch(() => {});
    },
    /** 查看详细数据 */
    handleViewDetails() {
      if (!this.queryParams.startDate || !this.queryParams.endDate) {
        this.$modal.msgError("请先选择日期范围");
        return;
      }

      this.$router.push({
        path: '/worker/dailyReportDetail',
        query: {
          ...this.queryParams,
          startDate: this.queryParams.startDate,
          endDate: this.queryParams.endDate
        }
      });
    },
    /** 格式化时长（秒转小时） */
    formatHours(seconds) {
      if (seconds == null || seconds === '') {
        return '0.00';
      }
      const hours = seconds / 3600;
      return hours.toFixed(2);
    },
    /** 格式化日期 */
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
