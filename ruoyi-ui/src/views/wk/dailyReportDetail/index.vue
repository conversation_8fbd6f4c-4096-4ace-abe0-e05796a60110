<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="日期范围" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          @change="handleDateChange"
        />
      </el-form-item>
      <el-form-item label="姓名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="部门" prop="deptName">
        <el-input
          v-model="queryParams.deptName"
          placeholder="请输入部门名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button icon="el-icon-back" size="mini" @click="handleBack">返回</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出详细数据</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 详细数据表格 -->
    <el-table v-loading="loading" :data="detailList" border>
      <el-table-column label="工单编号" align="center" prop="taskCode" width="120" />
      <el-table-column label="工单名称" align="center" prop="taskName" width="150" />
      <el-table-column label="姓名" align="center" prop="userName" width="100" />
      <el-table-column label="部门" align="center" prop="deptName" width="120" />
      <el-table-column label="项目" align="center" prop="projectName" width="120" />
      <el-table-column label="开始时间" align="center" width="150">
        <template slot-scope="scope">
          {{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center" width="150">
        <template slot-scope="scope">
          {{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </template>
      </el-table-column>
      <el-table-column label="上传时间" align="center" width="150">
        <template slot-scope="scope">
          {{ parseTime(scope.row.uploadTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </template>
      </el-table-column>
      <el-table-column label="工作时长(分钟)" align="center" width="120">
        <template slot-scope="scope">
          {{ scope.row.workDurationHours }}
        </template>
      </el-table-column>
      <el-table-column label="有效时长(分钟)" align="center" width="120">
        <template slot-scope="scope">
          {{ scope.row.effectiveDurationHours }}
        </template>
      </el-table-column>
      <el-table-column label="非有效时长(分钟)" align="center" width="130">
        <template slot-scope="scope">
          {{ scope.row.ineffectiveDurationHours }}
        </template>
      </el-table-column>
      <el-table-column label="间隔时间(分钟)" align="center" width="120">
        <template slot-scope="scope">
          {{ scope.row.intervalHours }}
        </template>
      </el-table-column>
      <el-table-column label="合格指标" align="center" prop="qualifiedCount" width="100" />
      <el-table-column label="不合格指标" align="center" prop="unqualifiedCount" width="110" />
      <el-table-column label="指标合计" align="center" prop="totalIndicators" width="100" />
      <el-table-column label="绩效点" align="center" prop="performancePoints" width="80" />
      <el-table-column label="绩效系数" align="center" prop="performanceCoefficient" width="100" />
      <el-table-column label="有效距离(米)" align="center" prop="effectiveKilometers" width="100" />
      <el-table-column label="非有效距离(米)" align="center" prop="ineffectiveKilometers" width="110" />
      <el-table-column label="总距离(米)" align="center" prop="totalKilometers" width="100" />
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getDailyReportDetails } from "@/api/worker/dailyReport";

export default {
  name: "DailyReportDetail",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 详细数据列表
      detailList: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        startDate: null,
        endDate: null,
        userName: null,
        deptName: null,
        projectName: null,
      }
    };
  },
  created() {
    // 从路由参数中获取查询条件
    const query = this.$route.query;
    if (query.startDate && query.endDate) {
      this.queryParams.startDate = query.startDate;
      this.queryParams.endDate = query.endDate;
      this.dateRange = [query.startDate, query.endDate];
    }
    if (query.userName) {
      this.queryParams.userName = query.userName;
    }
    if (query.deptName) {
      this.queryParams.deptName = query.deptName;
    }
    if (query.projectName) {
      this.queryParams.projectName = query.projectName;
    }

    this.getList();
  },
  methods: {
    /** 查询详细数据列表 */
    getList() {
      this.loading = true;
      getDailyReportDetails(this.queryParams).then(response => {
        this.detailList = response.data;
        this.total = response.data.length;
        this.loading = false;
      });
    },
    /** 日期范围改变 */
    handleDateChange(dateRange) {
      if (dateRange && dateRange.length === 2) {
        this.queryParams.startDate = dateRange[0];
        this.queryParams.endDate = dateRange[1];
      } else {
        this.queryParams.startDate = null;
        this.queryParams.endDate = null;
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        startDate: null,
        endDate: null,
        userName: null,
        deptName: null,
        projectName: null,
      };
      this.resetForm("queryForm");
      this.getList();
    },
    /** 返回按钮操作 */
    handleBack() {
      this.$router.go(-1);
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('worker/dailyReport/export', {
        ...this.queryParams,
        reportType: 4 // 详细数据导出类型
      }, `daily_report_detail_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
