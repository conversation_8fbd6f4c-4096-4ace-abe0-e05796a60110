package com.xz.worker.controller;

import com.github.pagehelper.PageInfo;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.xz.worker.domain.WkDailyReport;
import com.xz.worker.domain.vo.DailyReportQuery;
import com.xz.worker.service.IWkDailyReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 日报统计Controller
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
@RestController
@RequestMapping("/worker/dailyReport")
public class WkDailyReportController extends BaseController {
    
    @Autowired
    private IWkDailyReportService wkDailyReportService;

//    /**
//     * 查询人员维度日报统计
//     */
//    @PreAuthorize("@ss.hasPermi('worker:dailyReport:list')")
//    @PostMapping("/user")
//    public AjaxResult getUserDailyReport(@RequestBody DailyReportQuery query) {
//        query.setReportType(1);
//        PageInfo<WkDailyReport> pageInfo = wkDailyReportService.selectUserDailyReport(query);
//        return success(pageInfo);
//    }
//
//    /**
//     * 查询部门维度日报统计
//     */
//    @PreAuthorize("@ss.hasPermi('worker:dailyReport:list')")
//    @PostMapping("/dept")
//    public AjaxResult getDeptDailyReport(@RequestBody DailyReportQuery query) {
//        query.setReportType(2);
//        PageInfo<WkDailyReport> pageInfo = wkDailyReportService.selectDeptDailyReport(query);
//        return success(pageInfo);
//    }
//
//    /**
//     * 查询项目维度日报统计
//     */
//    @PreAuthorize("@ss.hasPermi('worker:dailyReport:list')")
//    @PostMapping("/project")
//    public AjaxResult getProjectDailyReport(@RequestBody DailyReportQuery query) {
//        query.setReportType(3);
//        PageInfo<WkDailyReport> pageInfo = wkDailyReportService.selectProjectDailyReport(query);
//        return success(pageInfo);
//    }
//
//    /**
//     * 导出日报统计数据
//     */
//    @PreAuthorize("@ss.hasPermi('worker:dailyReport:export')")
//    @Log(title = "日报统计", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, @RequestBody DailyReportQuery query) {
//        List<WkDailyReport> list = wkDailyReportService.exportDailyReport(query);
//        ExcelUtil<WkDailyReport> util = new ExcelUtil<WkDailyReport>(WkDailyReport.class);
//
//        String fileName = "日报统计数据";
//        switch (query.getReportType()) {
//            case 1:
//                fileName = "人员维度日报统计";
//                break;
//            case 2:
//                fileName = "部门维度日报统计";
//                break;
//            case 3:
//                fileName = "项目维度日报统计";
//                break;
//        }
//
//        util.exportExcel(response, list, fileName);
//    }
}
