package com.xz.worker.mapper;

import com.ruoyi.common.mapper.BaseMapperPlus;
import com.xz.worker.domain.WkTaskPicture;
import com.xz.worker.domain.WkTaskPictureInfo;
import com.xz.worker.domain.vo.WkTaskPictureVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 图片Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-07-14
 */
public interface WkTaskPictureMapper extends BaseMapperPlus<WkTaskPictureMapper, WkTaskPicture, WkTaskPictureVo>
{
    /**
     * 查询图片
     * 
     * @param taskPictureId 图片主键
     * @return 图片
     */
    public WkTaskPicture selectWkTaskPictureByTaskPictureId(Long taskPictureId);

    /**
     * 查询图片列表
     * 
     * @param wkTaskPicture 图片
     * @return 图片集合
     */
    public List<WkTaskPicture> selectWkTaskPictureList(WkTaskPicture wkTaskPicture);

    /**
     * 根据时间查询所有的照片信息
     * @param startDate
     * @return
     */
    public List<WkTaskPictureInfo> selectAllPicInfos(@Param("startDate") String startDate);

    public List<WkTaskPicture> getUnIdentifyCityIndex();

    public List<WkTaskPicture> selectLastWkTaskPictureList(WkTaskPicture wkTaskPicture);

    /**
     * 根据项目id以及指标获取图片
     * @param labelId
     * @param projectIds
     * @return
     */
    public List<String> selectPicsByCityIndex(@Param("labelId")Long labelId, @Param("projectIds")List<Long> projectIds);

    /**
     * 根据工单ids查询图片列表
     * @param list
     * @return
     */
    public List<WkTaskPicture> selectWkTaskPictureByTaskIds(List<Long> list);

    /**
     * 新增图片
     * 
     * @param wkTaskPicture 图片
     * @return 结果
     */
    public int insertWkTaskPicture(WkTaskPicture wkTaskPicture);

    /**
     * 批量更新照片的推送状态
     * @param list
     * @param hasPushFlag
     * @return
     */
    public int updateWkTaskPictureByIds(@Param("list") List<Long> list, @Param("hasPushFlag") Integer hasPushFlag);
    public int updateReFormStatusByTaskId(@Param("taskId") Long taskId);
    public int updateWkTaskPicturePushReFormByIds(@Param("list") List<Long> list, @Param("hasPushReformFlag") Integer hasPushReformFlag);

    /**
     *
     * @param limit
     * @return
     */
    public List<WkTaskPicture> getPushPicsData(@Param("limit")Integer limit);
    public List<WkTaskPicture> getNeedPushReformSysTemPics(@Param("limit")Integer limit);

    /**
     * 批量新增图片
     *
     * @param list 图片
     * @return 结果
     */
    public int insertList(List<WkTaskPicture> list);

    /**
     * 修改图片
     * 
     * @param wkTaskPicture 图片
     * @return 结果
     */
    public int updateWkTaskPicture(WkTaskPicture wkTaskPicture);

    /**
     * 删除图片
     * 
     * @param taskPictureId 图片主键
     * @return 结果
     */
    public int deleteWkTaskPictureByTaskPictureId(Long taskPictureId);

    /**
     * 批量删除图片
     * 
     * @param taskPictureIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWkTaskPictureByTaskPictureIds(Long[] taskPictureIds);

}
