package com.xz.worker.mapper;

import java.util.List;
import com.xz.worker.domain.WkWorkDetail;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 工作轨迹副Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-01-29
 */
public interface WkWorkDetailMapper 
{
    /**
     * 查询工作轨迹副
     * 
     * @param workDetailId 工作轨迹副主键
     * @return 工作轨迹副
     */
    public WkWorkDetail selectWkWorkDetailByWorkDetailId(@Param("workDetailId") Long workDetailId);

    /**
     * 查询工作轨迹副列表
     * 
     * @param wkWorkDetail 工作轨迹副
     * @return 工作轨迹副集合
     */
    public List<WkWorkDetail> selectWkWorkDetailList(WkWorkDetail wkWorkDetail);

    public List<WkWorkDetail> selectWkWorkDetailListV1(WkWorkDetail wkWorkDetail);

    /**
     * 查询工作轨迹副列表(根据月表)
     *
     * @param wkWorkDetail 工作轨迹副
     * @return 工作轨迹副集合
     */
    public List<WkWorkDetail> selectWkWorkDetailListToAdmin(WkWorkDetail wkWorkDetail);

    /**
     * 根据在线用户获取未计算的轨迹
     * @param users
     * @return
     */
    public List<WkWorkDetail> selectWkWorkDetailListByOnlineUsers(@Param("users") List<Long> users, @Param("month") String month);

    /**
     * 新增工作轨迹副
     * 
     * @param wkWorkDetail 工作轨迹副
     * @return 结果
     */
    public int insertWkWorkDetail(WkWorkDetail wkWorkDetail);

    /**
     * 修改工作轨迹副
     * 
     * @param wkWorkDetail 工作轨迹副
     * @return 结果
     */
    public int updateWkWorkDetail(WkWorkDetail wkWorkDetail);

    /**
     * 批量插入工单轨迹
     * @param
     * @return
     */
    public int insertList(WkWorkDetail wkWorkDetail);

    /**
     * 删除工作轨迹副
     * 
     * @param workDetailId 工作轨迹副主键
     * @return 结果
     */
    public int deleteWkWorkDetailByWorkDetailId(Long workDetailId);

    /**
     * 批量删除工作轨迹副
     * 
     * @param workDetailIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWkWorkDetailByWorkDetailIds(Long[] workDetailIds);

    /**
     * 获取用户的最近工作轨迹
     * @param userId
     * @return
     */
    public List<WkWorkDetail> getWorkorderTracks(@Param("userId") Long userId, @Param("month") String month);

    /**
     * 更新轨迹已经计算过
     * @param workDetailIds
     * @return
     */
    public int updateWorkDetailIsCal(@Param("workDetailIds") List<Long> workDetailIds, @Param("month") String month);


    @Select("SELECT task_id, lat, lon, create_time FROM ${tableName} WHERE task_id IN (${taskIds}) ORDER BY task_id, create_time")
    List<WkWorkDetail> getWorkorderTracksByTaskIds(@Param("tableName") String tableName, @Param("taskIds") String taskIds);

}
