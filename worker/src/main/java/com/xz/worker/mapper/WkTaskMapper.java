package com.xz.worker.mapper;

import com.xz.worker.domain.ParentCLabel;
import com.xz.worker.domain.WkTask;
import com.xz.worker.domain.WkTaskPicture;
import com.xz.worker.domain.WkTaskReviewHistory;
import com.xz.worker.domain.dto.TaskReviewData;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 工单列表Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-07-12
 */
public interface WkTaskMapper
{
    /**
     * 查询工单列表
     * 
     * @param taskId 工单列表主键
     * @return 工单列表
     */
    public WkTask selectWkTaskByTaskId(Long taskId);

    /**
     * 获取用户未完成的工单数量
     * @param userId
     * @return
     */
    public int getCurrentUnReviewCompleteTaskNum(Long userId);

    public List<Long> getRushTasksByRand(@Param("limit") Integer limit, @Param("type") Integer type);

    /**
     * 查询工单列表列表
     * 
     * @param wkTask 工单列表
     * @return 工单列表集合
     */
    public List<WkTask> selectWkTaskList(WkTask wkTask);

    /**
     * 查询工单列表列表
     *
     * @param wkTask 工单列表
     * @return 工单列表集合
     */
    public List<WkTask> selectWkTaskListToApp(WkTask wkTask);

    /**
     * 工单列表ids
     * @param wkTask
     * @return
     */
    public List<WkTask> selectWkTaskIds(WkTask wkTask);

    /**
     * 查询工单列表列表
     *
     * @param list 工单列表
     * @return 工单列表集合
     */
    public List<WkTask> selectWkTaskListByCodes(List<String> list);

    /**
     * 获取需要自动一审通过的工单
     * @param principalIds
     * @return
     */
    public List<WkTask> selectWkTasksOnAutoPass(@Param("principalIds") List<Long> principalIds);

    /**
     * 获取工单审核数据
     * @param wkTask
     * @return
     */
    public List<TaskReviewData> selectTaskReviewData(WkTask wkTask);

    /**
     * 新增工单列表
     * 
     * @param wkTask 工单列表
     * @return 结果
     */
    public int insertWkTask(WkTask wkTask);

    /**
     * 修改工单列表
     * 
     * @param wkTask 工单列表
     * @return 结果
     */
    public int updateWkTask(WkTask wkTask);

    /**
     * 批量更新一审通过二审待审核状态
     * @param taskIds
     * @return
     */
    public int updateAutoAudit1Pass(@Param("taskIds") List<Long> taskIds);

    public Long getNextTaskId(@Param("userId") Long userId, @Param("period") Long period, @Param("projectId") Long projectId, @Param("gisId")Long gisId);

    /**
     * 批量分配工单
     * @param principalId
     * @param list
     * @return
     */
    public int batchAttach(@Param("principalId") Long principalId, @Param("list") List<Long> list);

    /**
     * 批量查询工单
     * @param list
     * @return
     */
    public List<WkTask> selectWkTaskListByIds(@Param("list") List<Long> list);

    /**
     * 获取车查工单是否已经存在
     * @param list
     * @return
     */
    public List<WkTask> selectWkTaskListByGisIds(@Param("list") List<Long> list);

    public int insertList(List<WkTask> list);

    /**
     * 批量更新计算状态
     * @param list
     * @return
     */
    public int batchUpdateIsCal(List<Long> list);

    /**
     * 批量更新工单
     * @param reviewRemark
     * @param audit1Remark
     * @param audit2Remark
     * @param reviewReason
     * @param audit1Reason
     * @param audit2Reason
     * @param list
     * @return
     */
    public int updateWkTaskByIds(@Param("status") Long status,@Param("reviewStatus") Long reviewStatus,@Param("audit1status") Long audit1status,@Param("audit2status") Long audit2status,
                                 @Param("reviewRemark") String reviewRemark,@Param("audit1Remark") String audit1Remark,
                                 @Param("audit2Remark") String audit2Remark,@Param("reviewReason") String reviewReason,
                                 @Param("audit1Reason") String audit1Reason, @Param("audit2Reason") String audit2Reason,  @Param("list") List<Long> list, @Param("hasCal") Integer hasCal, @Param("hasPushFlag") Integer hasPushFlag);


    /**
     * 批量安排工单
     * @param audit1byId
     * @param list
     * @return
     */
    public int batchPlan(@Param("audit1byId") Long audit1byId, @Param("audit2byId") Long audit2byId, @Param("reviewById") Long reviewById, @Param("list") List<Long> list);

    /**
     * 删除工单列表
     * 
     * @param taskId 工单列表主键
     * @return 结果
     */
    public int deleteWkTaskByTaskId(Long taskId);

    /**
     * 批量删除工单列表
     * 
     * @param taskIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWkTaskByTaskIds(Long[] taskIds);

    List<WkTaskReviewHistory> getWkTaskReviewHistoryByTaskIds(List<Long> list);

    public List<ParentCLabel> getParentCLabels();

    List<String> findQuestionForExport(Long appId);

    /**
     * 根据工单ids查询图片列表
     * @param list
     * @return
     */
    public List<WkTaskPicture> selectWkTaskPictureByTaskIds(List<Long> list);

    List<Map<String, Object>> findAnswerForExport(Long taskId);

    /**
     * 查询指定状态和日期范围内的工单列表
     *
     * @param startDate 查询日期
     * @param endDate 查询日期
     * @return 工单列表集合
     */
    public List<WkTask> selectWkTaskListByStatusAndDate(@Param("startDate") String startDate, @Param("endDate") String endDate);
}
