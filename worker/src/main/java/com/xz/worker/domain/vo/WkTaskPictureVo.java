package com.xz.worker.domain.vo;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 图片对象 wk_task_picture
 * 
 * <AUTHOR>
 * @date 2023-07-14
 */
@Data
public class WkTaskPictureVo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 图片id */
    private Long taskPictureId;

    /** 工单id */
    @Excel(name = "工单id")
    private Long taskId;

    private Long areaId;

    /** 问题内容 */
    @Excel(name = "问题内容")
    private String url;

    /** 位置 */
    @Excel(name = "位置")
    private String point;

    /** 类型 1-门头 2-小区单元 3-营业执照 */
    @Excel(name = "类型 1-门头 2-其他")
    private Long type;

    private Integer uploadNum;

    private String realLocation;

    private Long cityIndex;

    private String address;
    private String remark;

    private String defaultAddress;

    private String location;

    private Double lon;

    private Double lat;

    private String srcIndexName;

    private String cityIndexName;

    // 机器识别指标
    private String aiCityIndexName;
    private Long aiCityIndex;

    private Long principalId;

    private Long audit1byId;

    private Long audit2byId;

    private Long reviewById;

    private String indexName;
    private String indexName1;
    private String indexName2;
    private String code;
    private String name;
    private String pName;
    private String seatId;

    private Integer reformStatus;

    /**
     * 1:一审修改 2：二审修改 3：复核修改
     */
    private Integer source;

    private Long projectId;

    /**
     * 类型 0：工作流 1：实效2：车查
     */
    private Integer taskType;

    /**
     * 1：指标修改 2：内容修改
     */
    private Integer modType;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    private Long srcCityIndex;

    private Long oldLabelId;
    private String newData;
    private String oldData;

    private String time;

    private String superiorDate;

    private String subUrl;

    private Integer hasPushFlag;

    /**
     * 扩展字段
     */
    private String ext;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("taskPictureId", getTaskPictureId())
            .append("taskId", getTaskId())
            .append("url", getUrl())
            .append("point", getPoint())
            .append("type", getType())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
