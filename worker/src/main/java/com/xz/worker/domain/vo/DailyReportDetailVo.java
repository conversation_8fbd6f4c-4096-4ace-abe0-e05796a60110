package com.xz.worker.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 日报详细数据VO
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
public class DailyReportDetailVo {

    /** 工单ID */
    private Long taskId;

    /** 工单编号 */
    private String taskCode;

    /** 工单名称 */
    private String taskName;

    /** 用户ID */
    private Long userId;

    /** 用户姓名 */
    private String userName;

    /** 部门ID */
    private Long deptId;

    /** 部门名称 */
    private String deptName;

    /** 项目ID */
    private Long projectId;

    /** 项目名称 */
    private String projectName;

    /** 工单开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 工单结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 上传完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date uploadTime;

    /** 工作时长（秒） */
    private Long workDurationSeconds;

    /** 工作时长（小时） */
    private BigDecimal workDurationHours;

    /** 有效时长（秒） */
    private Long effectiveDurationSeconds;

    /** 有效时长（小时） */
    private BigDecimal effectiveDurationHours;

    /** 非有效时长（秒） */
    private Long ineffectiveDurationSeconds;

    /** 非有效时长（小时） */
    private BigDecimal ineffectiveDurationHours;

    /** 合格指标数量 */
    private Long qualifiedCount;

    /** 不合格指标数量 */
    private Long unqualifiedCount;

    /** 指标合计 */
    private Long totalIndicators;

    /** 绩效点 */
    private BigDecimal performancePoints;

    /** 有效公里数 */
    private BigDecimal effectiveKilometers;

    /** 非有效公里数 */
    private BigDecimal ineffectiveKilometers;

    /** 公里数合计 */
    private BigDecimal totalKilometers;

    /** 绩效系数 */
    private BigDecimal performanceCoefficient;

    /** 与上一个工单的间隔时间（秒） */
    private Long intervalSeconds;

    /** 与上一个工单的间隔时间（小时） */
    private BigDecimal intervalHours;

    /** 第一张照片拍摄时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date firstPhotoTime;

    /** 最后一张照片拍摄时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastPhotoTime;
}
