package com.xz.worker.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 日报统计对象 wk_daily_report
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wk_daily_report")
public class WkDailyReport extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 日期
     */
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private LocalDate reportDate;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 姓名
     */
    private String userName;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 项目ID
     */
    private Long projectId;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 拍摄调研开始时间
     */
    private Date surveyStartTime;
    /**
     * 结束调研时间
     */
    private Date surveyEndTime;
    /**
     * 有效时长（秒）
     */
    private Long effectiveHours;
    /**
     * 非有效时长（秒）
     */
    private Long ineffectiveHours;
    /**
     * 工作时长（秒）
     */
    private Long workHours;
    /**
     * 每个工单的间隔时间（秒）
     */
    private Long avgIntervalMinutes;
    /**
     * 不合格指标数量
     */
    private Long unqualifiedCount;
    /**
     * 合格指标数量
     */
    private Long qualifiedCount;
    /**
     * 指标合计
     */
    private Long totalIndicators;
    /**
     * 工单数
     */
    private Long workOrderCount;
    /**
     * 绩效点
     */
    private BigDecimal performancePoints;
    /**
     * 有效公里数
     */
    private BigDecimal effectiveKilometers;
    /**
     * 非有效公里数
     */
    private BigDecimal ineffectiveKilometers;
    /**
     * 公里数合计
     */
    private BigDecimal totalKilometers;
    /**
     * 上传完成时间
     */
    private Date uploadTime;
    /**
     * 绩效系数
     */
    private BigDecimal performanceCoefficient;

}
