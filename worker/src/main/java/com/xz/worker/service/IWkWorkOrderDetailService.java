package com.xz.worker.service;

import java.util.List;
import java.util.Map;

import com.alibaba.fastjson2.JSONObject;
import com.xz.worker.domain.TimeOutWorkOrder;
import com.xz.worker.domain.WkWorkDetail;
import com.xz.worker.domain.WkWorkOrder;
import com.xz.worker.domain.WkWorkOrderDetail;
import com.xz.worker.domain.statistics.ReportInfo;
import com.xz.worker.domain.statistics.WorkTotalResult;
import com.xz.worker.domain.statistics.WorkorderDeptResult;
import com.xz.worker.domain.vo.QueryBigScreen;
import com.xz.worker.domain.vo.TaskUpdateAssigneeReqVO;

/**
 * 工单处理关联信息Service接口
 * 
 * <AUTHOR>
 * @date 2023-02-03
 */
public interface IWkWorkOrderDetailService 
{
    /**
     * 查询工单处理关联信息
     * 
     * @param orderDetailId 工单处理关联信息主键
     * @return 工单处理关联信息
     */
    public WkWorkOrderDetail selectWkWorkOrderDetailByOrderDetailId(Long orderDetailId);

    /**
     * 查询工单处理关联信息列表
     * 
     * @param wkWorkOrderDetail 工单处理关联信息
     * @return 工单处理关联信息集合
     */
    public List<WkWorkOrderDetail> selectWkWorkOrderDetailList(WkWorkOrderDetail wkWorkOrderDetail);

    /**
     * 查询工单处理关联信息列表（管理超时）
     *
     * @param wkWorkOrderDetail 工单处理关联信息
     * @return 工单处理关联信息集合
     */
    public List<WkWorkOrderDetail> selectWkWorkOrderDetailListOnTimeout(WkWorkOrderDetail wkWorkOrderDetail);

    /**
     * 新增工单处理关联信息
     * 
     * @param wkWorkOrderDetail 工单处理关联信息
     * @return 结果
     */
    public int insertWkWorkOrderDetail(WkWorkOrderDetail wkWorkOrderDetail);

    /**
     * 批量新增工单处理关联信息
     *
     * @param list 工单处理关联信息
     * @return 结果
     */
    public void insertList(List<WkWorkOrderDetail> list);

    /**
     * 修改工单处理关联信息
     * 
     * @param wkWorkOrderDetail 工单处理关联信息
     * @return 结果
     */
    public int updateWkWorkOrderDetail(WkWorkOrderDetail wkWorkOrderDetail);

    /**
     * 批量删除工单处理关联信息
     * 
     * @param orderDetailIds 需要删除的工单处理关联信息主键集合
     * @return 结果
     */
    public int deleteWkWorkOrderDetailByOrderDetailIds(Long[] orderDetailIds);

    /**
     * 删除工单处理关联信息信息
     * 
     * @param orderDetailId 工单处理关联信息主键
     * @return 结果
     */
    public int deleteWkWorkOrderDetailByOrderDetailId(Long orderDetailId);

    /**
     * 找出这些用户中正在处理工单最少的用户id
     *
     * @param userIds 用户ids
     * @return 结果
     */
    public Long findMinWorksUserId(List<Long> userIds);


    /**
     * 查找未完成的工单(管理)
     *
     * @return 结果
     */
    public List<TimeOutWorkOrder> findWillTimeOutWorkOrder();

    /**
     * 查找未完成的工单（用户）
     *
     * @return 结果
     */
    public List<TimeOutWorkOrder> findWillTimeOutWorkOrderUser();

    /**
     * 获取整个工单的过程
     *
     * @return 结果
     */
    public List<WkWorkOrderDetail> getWkWorkOrderDetails(Long workOrderId);

    /**
     * 更新工单关联信息
     *
     */
    public void updateWorkOrderDetails(Long userId, WkWorkOrderDetail wkWorkOrderDetail);


    /**
     * 领导转派工单
     * @param taskUpdateAssigneeReqVO
     */
    public void leaderUpdateTaskAssignee(Long leaderId, TaskUpdateAssigneeReqVO taskUpdateAssigneeReqVO);

    /**
     * 内部转派工单
     * @param taskUpdateAssigneeReqVO
     */
    public void updateTaskAssignee(Long leaderId, TaskUpdateAssigneeReqVO taskUpdateAssigneeReqVO);

    /**
     * 工单申述
     * @param taskUpdateAssigneeReqVO
     */
    public void updateTaskAppeal(Long userId, TaskUpdateAssigneeReqVO taskUpdateAssigneeReqVO);

    /**
     * 获取工单部门统计数量
     * @return
     */
    public Map<String, JSONObject> getWorkorderDeptNumbers(QueryBigScreen queryBigScreen);

    /**
     * 获取网格的工单数
     * @param type 1:月 2:当天 3:四小时以内
     * @return
     */
    public Map<String, JSONObject> getWorkorderGridNumbers(Integer type);

    /**
     * 获取网格数据
     * @param parentId
     * @return
     */
    public List<WorkTotalResult> getWorkOrdersByGrid(Long parentId);

    /**
     * 获取工单数
     * @param queryBigScreen 1:按月份 2:按天 3:按小时
     * @return
     */
    public Map<String, JSONObject> getWorkorderNumbers(QueryBigScreen queryBigScreen);

    public ReportInfo getReportInfo(QueryBigScreen queryBigScreen);

    /**
     * 按工单查询工作轨迹
     * @param taskIds
     * @return
     */
    List<WkWorkDetail> getWorkOrderTracksByTaskIds(List<Long> taskIds);
 }
