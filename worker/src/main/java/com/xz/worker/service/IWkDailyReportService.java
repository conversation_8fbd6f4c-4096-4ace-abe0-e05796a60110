package com.xz.worker.service;

import com.github.pagehelper.PageInfo;
import com.xz.worker.domain.WkDailyReport;
import com.xz.worker.domain.vo.DailyReportQuery;
import com.xz.worker.domain.vo.DailyReportDetailVo;

import java.time.LocalDate;
import java.util.List;

/**
 * 日报统计Service接口
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
public interface IWkDailyReportService {

    /**
     * 生成指定日期的日报数据
     *
     * @param reportDate 报告日期
     */
    void generateDailyReport(LocalDate reportDate);

    /**
     * 查询人员维度日报统计
     *
     * @param query 查询条件
     * @return 分页结果
     */
    PageInfo<WkDailyReport> selectUserDailyReport(DailyReportQuery query);

    /**
     * 查询部门维度日报统计
     *
     * @param query 查询条件
     * @return 分页结果
     */
    PageInfo<WkDailyReport> selectDeptDailyReport(DailyReportQuery query);

    /**
     * 查询项目维度日报统计
     *
     * @param query 查询条件
     * @return 分页结果
     */
    PageInfo<WkDailyReport> selectProjectDailyReport(DailyReportQuery query);

    /**
     * 查询日报详细数据
     *
     * @param query 查询条件
     * @return 详细数据列表
     */
    List<DailyReportDetailVo> selectDailyReportDetails(DailyReportQuery query);

    /**
     * 导出日报统计数据
     *
     * @param query 查询条件
     * @return 导出数据列表
     */
    List<WkDailyReport> exportDailyReport(DailyReportQuery query);
}
