package com.xz.worker.service;

import java.util.List;
import com.xz.worker.domain.WkWorkDetail;
import com.xz.worker.domain.mongo.UserTrackInfo;

/**
 * 工作轨迹副Service接口
 * 
 * <AUTHOR>
 * @date 2023-01-29
 */
public interface IWkWorkDetailService 
{
    /**
     * 查询工作轨迹副
     * 
     * @param workDetailId 工作轨迹副主键
     * @return 工作轨迹副
     */
    public WkWorkDetail selectWkWorkDetailByWorkDetailId(Long workDetailId);

    /**
     * 查询工作轨迹副
     *
     * @param wkWorkDetail 工作轨迹副主键
     * @return 工作轨迹副
     */
    public List<WkWorkDetail> getTaskWorkdetails(WkWorkDetail wkWorkDetail);

    /**
     * 查询工作轨迹副列表(根据月表)
     *
     * @param wkWorkDetail 工作轨迹副
     * @return 工作轨迹副集合
     */
    public List<WkWorkDetail> selectWkWorkDetailListToAdmin(WkWorkDetail wkWorkDetail);

    public List<UserTrackInfo> selectWkWorkDetailListToAdminOnMongo(WkWorkDetail wkWorkDetail);

    /**
     * 查询工作轨迹副列表
     * 
     * @param wkWorkDetail 工作轨迹副
     * @return 工作轨迹副集合
     */
    public List<WkWorkDetail> selectWkWorkDetailList(WkWorkDetail wkWorkDetail);

    public List<WkWorkDetail> selectWkWorkDetailListV1(WkWorkDetail wkWorkDetail);

    /**
     * 新增工作轨迹副
     * 
     * @param wkWorkDetail 工作轨迹副
     * @return 结果
     */
    public int insertWkWorkDetail(WkWorkDetail wkWorkDetail);

    /**
     * 批量插入工单轨迹
     * @param list
     * @return
     */
    public int insertList(List<WkWorkDetail> list);

    /**
     * 修改工作轨迹副
     * 
     * @param wkWorkDetail 工作轨迹副
     * @return 结果
     */
    public int updateWkWorkDetail(WkWorkDetail wkWorkDetail);

    /**
     * 批量删除工作轨迹副
     * 
     * @param workDetailIds 需要删除的工作轨迹副主键集合
     * @return 结果
     */
    public int deleteWkWorkDetailByWorkDetailIds(Long[] workDetailIds);

    /**
     * 删除工作轨迹副信息
     * 
     * @param workDetailId 工作轨迹副主键
     * @return 结果
     */
    public int deleteWkWorkDetailByWorkDetailId(Long workDetailId);

    /**
     * 获取用户的最近工作轨迹
     * @param userId
     * @return
     */
    public List<WkWorkDetail> getWorkorderTracks(Long userId);

    List<WkWorkDetail> getWorkOrderTracksByTaskIds(List<Long> taskIds);
}
