package com.xz.worker.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.system.service.ISysUserService;
import com.xz.worker.controller.utils.CalculateDistance;
import com.xz.worker.domain.WkDailyReport;
import com.xz.worker.domain.WkTask;
import com.xz.worker.domain.WkTaskPicture;
import com.xz.worker.domain.WkWorkDetail;
import com.xz.worker.mapper.WkDailyReportMapper;
import com.xz.worker.mapper.WkTaskMapper;
import com.xz.worker.mapper.WkTaskPictureMapper;
import com.xz.worker.service.IWkDailyReportService;
import com.xz.worker.service.IWkWorkDetailService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 日报统计Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
@Service
public class WkDailyReportServiceImpl implements IWkDailyReportService {
    
    @Autowired
    private WkDailyReportMapper wkDailyReportMapper;

    @Autowired
    private WkTaskMapper wkTaskMapper;

    @Autowired
    private WkTaskPictureMapper wkTaskPictureMapper;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private IWkWorkDetailService wkWorkDetailService;

    private static final Logger log = LoggerFactory.getLogger(WkDailyReportServiceImpl.class);

    @Override
    public void generateDailyReport(LocalDate reportDate) {

        log.info("开始生成{}的日报数据", reportDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));

        try {
            // 1. 清理当日已有数据
            int deletedCount = wkDailyReportMapper.delete(new LambdaQueryWrapper<WkDailyReport>()
                    .eq(WkDailyReport::getReportDate, reportDate));
            log.info("清理{}已有日报数据{}条", reportDate, deletedCount);

            // 2. 插入日报表数据
            List<WkTask> wkTasks = wkTaskMapper.selectList(new LambdaQueryWrapper<WkTask>()
                    .eq(WkTask::getStatus, 3)
                    .between(WkTask::getEndDate, reportDate, reportDate.plusDays(1)));

            List<SysUser> sysUsers = userService.selectAll(new SysUser());
            Map<Long, SysUser> sysUserMap = sysUsers.stream().collect(Collectors.toMap(SysUser::getUserId, t -> t));

            List<Long> wkTaskIds = wkTasks.stream().map(WkTask::getTaskId).collect(Collectors.toList());
            List<WkTaskPicture> wkTaskPictures = wkTaskPictureMapper.selectList(new LambdaQueryWrapper<WkTaskPicture>()
                    .select(WkTaskPicture::getTaskId, WkTaskPicture::getCityIndex)
                    .in(WkTaskPicture::getTaskId, wkTaskIds));

            // 统计每个taskId中CityIndex等于-1和大于0的数量
            Map<Long, Map<String, Long>> cityIndexCount = wkTaskPictures.stream()
                    .collect(Collectors.groupingBy(WkTaskPicture::getTaskId,
                            Collectors.groupingBy(
                                    picture -> {
                                        if (picture.getCityIndex() == -1) {
                                            return "pass";
                                        } else if (picture.getCityIndex() > 0) {
                                            return "noPass";
                                        } else {
                                            return "others";
                                        }
                                    },
                                    Collectors.counting()
                            )
                    ));

            // 按用户分组并处理工单
            Map<Long, List<WkTask>> userTasksMap = wkTasks.stream()
                    .collect(Collectors.groupingBy(WkTask::getPrincipalId));

            // 查询有效公里数
            List<WkWorkDetail> workDetails = wkWorkDetailService.getWorkOrderTracksByTaskIds(wkTaskIds);
            Map<Long, List<WkWorkDetail>> workDetailsMap = workDetails.stream()
                    .filter(detail -> detail.getLat() != null && detail.getLon() != null)
                    .collect(Collectors.groupingBy(WkWorkDetail::getTaskId));
            Map<Long, Double> distancesMap = calculateTaskDistances(workDetailsMap);

            List<WkDailyReport> wkDailyReports = new ArrayList<>();
            userTasksMap.forEach((userId, userTasks) -> {
                // 按结束时间排序
                userTasks.sort(Comparator.comparing(WkTask::getEndDate));

                for (int i = 0; i < userTasks.size(); i++) {
                    WkTask wkTask = userTasks.get(i);
                    WkDailyReport wkDailyReport = new WkDailyReport();
                    wkDailyReport.setReportDate(reportDate);
                    wkDailyReport.setUserId(wkTask.getPrincipalId());
                    wkDailyReport.setUserName(sysUserMap.get(wkTask.getPrincipalId()) == null ? null : sysUserMap.get(wkTask.getPrincipalId()).getUserName());
                    wkDailyReport.setDeptId(sysUserMap.get(wkTask.getPrincipalId()) == null ? null : sysUserMap.get(wkTask.getPrincipalId()).getDeptId());
                    wkDailyReport.setProjectId(wkTask.getProjectId());
                    wkDailyReport.setSurveyStartTime(wkTask.getStartDate());
                    wkDailyReport.setSurveyEndTime(wkTask.getEndDate());
                    wkDailyReport.setEffectiveHours(wkTask.getWorkTime());
                    // 计算非有效时长
                    long ineffectiveHours = 0;
                    long avgIntervalMinutes = 0;
                    if (i < userTasks.size() - 1) {
                        WkTask nextTask = userTasks.get(i + 1);
                        ineffectiveHours = (nextTask.getStartDate().getTime() - wkTask.getEndDate().getTime()) / 1000;
                        avgIntervalMinutes = (nextTask.getStartDate().getTime() - wkTask.getStartDate().getTime()) / 1000;
                    }
                    wkDailyReport.setIneffectiveHours(ineffectiveHours);
                    wkDailyReport.setWorkHours(wkDailyReport.getEffectiveHours() + wkDailyReport.getIneffectiveHours());
                    wkDailyReport.setAvgIntervalMinutes(avgIntervalMinutes);
                    wkDailyReport.setUnqualifiedCount(cityIndexCount.get(wkTask.getTaskId()).get("noPass"));
                    wkDailyReport.setQualifiedCount(cityIndexCount.get(wkTask.getTaskId()).get("pass"));
                    wkDailyReport.setTotalIndicators(wkDailyReport.getQualifiedCount() + wkDailyReport.getUnqualifiedCount() + cityIndexCount.get(wkTask.getTaskId()).get("others"));
                    // TODO 绩效点
                    wkDailyReport.setPerformancePoints(null);
                    // 有效公里数
                    wkDailyReport.setEffectiveKilometers(BigDecimal.valueOf(distancesMap.get(wkTask.getTaskId())==null?0:distancesMap.get(wkTask.getTaskId())));
                    // TODO 非有效公里数
                    wkDailyReport.setIneffectiveHours(0l);
                    wkDailyReport.setUploadTime(wkTask.getUpdateTime());
                    wkDailyReports.add(wkDailyReport);
                }
            });

            wkDailyReportMapper.insertBatch(wkDailyReports);

            log.info("完成生成{}的日报数据", reportDate);

        } catch (Exception e) {
            log.error("生成{}日报数据失败", reportDate, e);
            throw new RuntimeException("生成日报数据失败: " + e.getMessage(), e);
        }
    }

    private Map<Long, Double> calculateTaskDistances(Map<Long, List<WkWorkDetail>> taskWorkDetails) {
        return taskWorkDetails.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> {
                            List<WkWorkDetail> details = entry.getValue();
                            // 按create_time排序
                            details.sort(Comparator.comparing(WkWorkDetail::getCreateTime));

                            double totalDistance = 0.0;
                            for (int i = 0; i < details.size() - 1; i++) {
                                WkWorkDetail current = details.get(i);
                                WkWorkDetail next = details.get(i + 1);

                                // 计算相邻点之间的距离
                                double distance = CalculateDistance.calculateDistance(
                                        current.getLat(),
                                        current.getLon(),
                                        next.getLat(),
                                        next.getLon()
                                );
                                totalDistance += distance;
                            }
                            return totalDistance;
                        }
                ));
    }
}
