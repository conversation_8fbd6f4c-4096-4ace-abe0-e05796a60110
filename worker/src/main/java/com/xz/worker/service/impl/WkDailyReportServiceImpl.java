package com.xz.worker.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.service.ISysUserService;
import com.xz.worker.controller.utils.CalculateDistance;
import com.xz.worker.domain.WkDailyReport;
import com.xz.worker.domain.WkTask;
import com.xz.worker.domain.WkTaskPicture;
import com.xz.worker.domain.WkWorkDetail;
import com.xz.worker.domain.vo.DailyReportQuery;
import com.xz.worker.domain.vo.DailyReportDetailVo;
import com.xz.worker.mapper.WkDailyReportMapper;
import com.xz.worker.mapper.WkTaskMapper;
import com.xz.worker.mapper.WkTaskPictureMapper;
import com.xz.worker.mapper.WkPerformanceCoefficientMapper;
import com.xz.worker.service.IWkDailyReportService;
import com.xz.worker.service.IWkWorkDetailService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 日报统计Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
@Service
public class WkDailyReportServiceImpl implements IWkDailyReportService {
    
    @Autowired
    private WkDailyReportMapper wkDailyReportMapper;

    @Autowired
    private WkTaskMapper wkTaskMapper;

    @Autowired
    private WkTaskPictureMapper wkTaskPictureMapper;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private IWkWorkDetailService wkWorkDetailService;

    @Autowired
    private WkPerformanceCoefficientMapper performanceCoefficientMapper;

    private static final Logger log = LoggerFactory.getLogger(WkDailyReportServiceImpl.class);

    @Override
    public void generateDailyReport(LocalDate reportDate) {

        log.info("开始生成{}的日报数据", reportDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));

        try {
            // 1. 清理当日已有数据
            int deletedCount = wkDailyReportMapper.delete(new LambdaQueryWrapper<WkDailyReport>()
                    .eq(WkDailyReport::getReportDate, reportDate));
            log.info("清理{}已有日报数据{}条", reportDate, deletedCount);

            // 2. 插入日报表数据
            String startDate = reportDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String endDate = reportDate.plusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            List<WkTask> wkTasks = wkTaskMapper.selectWkTaskListByStatusAndDate(startDate, endDate);

            List<SysUser> sysUsers = userService.selectAll(new SysUser());
            Map<Long, SysUser> sysUserMap = sysUsers.stream().collect(Collectors.toMap(SysUser::getUserId, t -> t));

            List<Long> wkTaskIds = wkTasks.stream().map(WkTask::getTaskId).collect(Collectors.toList());
            List<WkTaskPicture> wkTaskPictures = wkTaskPictureMapper.selectList(new LambdaQueryWrapper<WkTaskPicture>()
                    .select(WkTaskPicture::getTaskId, WkTaskPicture::getCityIndex)
                    .in(WkTaskPicture::getTaskId, wkTaskIds));

            // 统计每个taskId中CityIndex等于-1和大于0的数量
            Map<Long, Map<String, Long>> cityIndexCount = wkTaskPictures.stream()
                    .collect(Collectors.groupingBy(WkTaskPicture::getTaskId,
                            Collectors.groupingBy(
                                    picture -> {
                                        if (picture.getCityIndex() == -1) {
                                            return "pass";
                                        } else if (picture.getCityIndex() > 0) {
                                            return "noPass";
                                        } else {
                                            return "others";
                                        }
                                    },
                                    Collectors.counting()
                            )
                    ));

            // 按用户分组并处理工单
            Map<Long, List<WkTask>> userTasksMap = wkTasks.stream()
                    .collect(Collectors.groupingBy(WkTask::getPrincipalId));

            // 查询有效公里数
            List<WkWorkDetail> workDetails = wkWorkDetailService.getWorkOrderTracksByTaskIds(wkTaskIds);
            Map<Long, List<WkWorkDetail>> workDetailsMap = workDetails.stream()
                    .filter(detail -> detail.getLat() != null && detail.getLon() != null)
                    .collect(Collectors.groupingBy(WkWorkDetail::getTaskId));
            Map<Long, Double> distancesMap = calculateTaskDistances(workDetailsMap);

            List<WkDailyReport> wkDailyReports = new ArrayList<>();
            userTasksMap.forEach((userId, userTasks) -> {
                // 按结束时间排序
                userTasks.sort(Comparator.comparing(WkTask::getEndDate));

                for (int i = 0; i < userTasks.size(); i++) {
                    WkTask wkTask = userTasks.get(i);
                    WkDailyReport wkDailyReport = new WkDailyReport();
                    wkDailyReport.setReportDate(reportDate);
                    wkDailyReport.setUserId(wkTask.getPrincipalId());
                    SysUser user = sysUserMap.get(wkTask.getPrincipalId());
                    if (user != null) {
                        wkDailyReport.setUserName(user.getUserName());
                        wkDailyReport.setDeptId(user.getDeptId());
                        // 这里需要获取部门名称，暂时设置为null，实际应用中需要查询部门表
                        wkDailyReport.setDeptName(null);
                    }
                    wkDailyReport.setProjectId(wkTask.getProjectId());
                    wkDailyReport.setProjectName(wkTask.getProjectName());
                    wkDailyReport.setSurveyStartTime(wkTask.getStartDate());
                    wkDailyReport.setSurveyEndTime(wkTask.getEndDate());
                    wkDailyReport.setEffectiveHours(wkTask.getWorkTime() != null ? wkTask.getWorkTime() : 0L);
                    // 计算非有效时长
                    long ineffectiveHours = 0;
                    long avgIntervalMinutes = 0;
                    if (i < userTasks.size() - 1) {
                        WkTask nextTask = userTasks.get(i + 1);
                        ineffectiveHours = (nextTask.getStartDate().getTime() - wkTask.getEndDate().getTime()) / 1000;
                        avgIntervalMinutes = (nextTask.getStartDate().getTime() - wkTask.getStartDate().getTime()) / 1000;
                    }
                    wkDailyReport.setIneffectiveHours(ineffectiveHours);
                    Long effectiveHours = wkDailyReport.getEffectiveHours() != null ? wkDailyReport.getEffectiveHours() : 0L;
                    Long ineffectiveHoursValue = wkDailyReport.getIneffectiveHours() != null ? wkDailyReport.getIneffectiveHours() : 0L;
                    wkDailyReport.setWorkHours(effectiveHours + ineffectiveHoursValue);
                    wkDailyReport.setAvgIntervalMinutes(avgIntervalMinutes);

                    // 安全获取指标统计数据
                    Map<String, Long> taskIndexCount = cityIndexCount.get(wkTask.getTaskId());
                    if (taskIndexCount != null) {
                        Long unqualified = taskIndexCount.get("noPass");
                        Long qualified = taskIndexCount.get("pass");
                        Long others = taskIndexCount.get("others");

                        wkDailyReport.setUnqualifiedCount(unqualified != null ? unqualified : 0L);
                        wkDailyReport.setQualifiedCount(qualified != null ? qualified : 0L);
                        wkDailyReport.setTotalIndicators((qualified != null ? qualified : 0L) +
                                                        (unqualified != null ? unqualified : 0L) +
                                                        (others != null ? others : 0L));
                    } else {
                        wkDailyReport.setUnqualifiedCount(0L);
                        wkDailyReport.setQualifiedCount(0L);
                        wkDailyReport.setTotalIndicators(0L);
                    }

                    // 计算绩效点
                    BigDecimal coefficient = performanceCoefficientMapper.selectCoefficientByProjectId(wkTask.getProjectId());
                    if (coefficient == null) {
                        coefficient = BigDecimal.ONE;
                    }
                    wkDailyReport.setPerformanceCoefficient(coefficient);
                    BigDecimal performancePoints = BigDecimal.valueOf(wkDailyReport.getQualifiedCount() != null ? wkDailyReport.getQualifiedCount() : 0L)
                            .multiply(coefficient);
                    wkDailyReport.setPerformancePoints(performancePoints);

                    // 有效公里数
                    Double distance = distancesMap.get(wkTask.getTaskId());
                    wkDailyReport.setEffectiveKilometers(BigDecimal.valueOf(distance != null ? distance : 0.0));
                    // TODO 非有效公里数
                    wkDailyReport.setIneffectiveKilometers(BigDecimal.ZERO);
                    wkDailyReport.setTotalKilometers(wkDailyReport.getEffectiveKilometers());
                    wkDailyReport.setUploadTime(wkTask.getUpdateTime());
                    wkDailyReport.setCreateTime(new Date());
                    wkDailyReports.add(wkDailyReport);
                }
            });

            wkDailyReportMapper.insertBatch(wkDailyReports);
            log.info("完成生成{}的日报数据", reportDate);

        } catch (Exception e) {
            log.error("生成{}日报数据失败", reportDate, e);
            throw new RuntimeException("生成日报数据失败: " + e.getMessage(), e);
        }
    }

    private Map<Long, Double> calculateTaskDistances(Map<Long, List<WkWorkDetail>> taskWorkDetails) {
        return taskWorkDetails.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> {
                            List<WkWorkDetail> details = entry.getValue();
                            // 按create_time排序
                            details.sort(Comparator.comparing(WkWorkDetail::getCreateTime));

                            double totalDistance = 0.0;
                            for (int i = 0; i < details.size() - 1; i++) {
                                WkWorkDetail current = details.get(i);
                                WkWorkDetail next = details.get(i + 1);

                                // 计算相邻点之间的距离
                                double distance = CalculateDistance.calculateDistance(
                                        current.getLat(),
                                        current.getLon(),
                                        next.getLat(),
                                        next.getLon()
                                );
                                totalDistance += distance;
                            }
                            return totalDistance;
                        }
                ));
    }

    @Override
    public PageInfo<WkDailyReport> selectUserDailyReport(DailyReportQuery query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());

        LambdaQueryWrapper<WkDailyReport> wrapper = new LambdaQueryWrapper<>();
        wrapper.between(query.getStartDate() != null && query.getEndDate() != null,
                WkDailyReport::getReportDate, query.getStartDate(), query.getEndDate());
        wrapper.like(StringUtils.isNotEmpty(query.getUserName()),
                WkDailyReport::getUserName, query.getUserName());
        wrapper.like(StringUtils.isNotEmpty(query.getProjectName()),
                WkDailyReport::getProjectName, query.getProjectName());
        wrapper.in(query.getUserIds() != null && !query.getUserIds().isEmpty(),
                WkDailyReport::getUserId, query.getUserIds());
        wrapper.in(query.getProjectIds() != null && !query.getProjectIds().isEmpty(),
                WkDailyReport::getProjectId, query.getProjectIds());
        wrapper.orderByDesc(WkDailyReport::getReportDate, WkDailyReport::getUserName);

        List<WkDailyReport> list = wkDailyReportMapper.selectList(wrapper);

        // 转换时长单位从秒到小时，并计算绩效点
        list.forEach(this::convertAndCalculateReport);

        return new PageInfo<>(list);
    }

    @Override
    public PageInfo<WkDailyReport> selectDeptDailyReport(DailyReportQuery query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());

        // 部门维度需要按部门聚合数据
        LambdaQueryWrapper<WkDailyReport> wrapper = new LambdaQueryWrapper<>();
        wrapper.between(query.getStartDate() != null && query.getEndDate() != null,
                WkDailyReport::getReportDate, query.getStartDate(), query.getEndDate());
        wrapper.like(StringUtils.isNotEmpty(query.getDeptName()),
                WkDailyReport::getDeptName, query.getDeptName());
        wrapper.like(StringUtils.isNotEmpty(query.getProjectName()),
                WkDailyReport::getProjectName, query.getProjectName());
        wrapper.in(query.getDeptIds() != null && !query.getDeptIds().isEmpty(),
                WkDailyReport::getDeptId, query.getDeptIds());
        wrapper.in(query.getProjectIds() != null && !query.getProjectIds().isEmpty(),
                WkDailyReport::getProjectId, query.getProjectIds());

        List<WkDailyReport> rawList = wkDailyReportMapper.selectList(wrapper);

        // 按日期、部门、项目分组聚合
        Map<String, List<WkDailyReport>> groupedData = rawList.stream()
                .collect(Collectors.groupingBy(report ->
                        report.getReportDate() + "_" + report.getDeptId() + "_" + report.getProjectId()));

        List<WkDailyReport> aggregatedList = new ArrayList<>();
        groupedData.forEach((key, reports) -> {
            WkDailyReport aggregated = aggregateDeptReports(reports);
            aggregatedList.add(aggregated);
        });

        aggregatedList.sort(Comparator.comparing(WkDailyReport::getReportDate).reversed()
                .thenComparing(WkDailyReport::getDeptName));

        return new PageInfo<>(aggregatedList);
    }

    @Override
    public PageInfo<WkDailyReport> selectProjectDailyReport(DailyReportQuery query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());

        // 项目维度需要按项目聚合数据
        LambdaQueryWrapper<WkDailyReport> wrapper = new LambdaQueryWrapper<>();
        wrapper.between(query.getStartDate() != null && query.getEndDate() != null,
                WkDailyReport::getReportDate, query.getStartDate(), query.getEndDate());
        wrapper.like(StringUtils.isNotEmpty(query.getProjectName()),
                WkDailyReport::getProjectName, query.getProjectName());
        wrapper.in(query.getProjectIds() != null && !query.getProjectIds().isEmpty(),
                WkDailyReport::getProjectId, query.getProjectIds());

        List<WkDailyReport> rawList = wkDailyReportMapper.selectList(wrapper);

        // 按日期、项目分组聚合
        Map<String, List<WkDailyReport>> groupedData = rawList.stream()
                .collect(Collectors.groupingBy(report ->
                        report.getReportDate() + "_" + report.getProjectId()));

        List<WkDailyReport> aggregatedList = new ArrayList<>();
        groupedData.forEach((key, reports) -> {
            WkDailyReport aggregated = aggregateProjectReports(reports);
            aggregatedList.add(aggregated);
        });

        aggregatedList.sort(Comparator.comparing(WkDailyReport::getReportDate).reversed()
                .thenComparing(WkDailyReport::getProjectName));

        return new PageInfo<>(aggregatedList);
    }

    @Override
    public List<DailyReportDetailVo> selectDailyReportDetails(DailyReportQuery query) {
        LambdaQueryWrapper<WkDailyReport> wrapper = new LambdaQueryWrapper<>();
        wrapper.between(query.getStartDate() != null && query.getEndDate() != null,
                WkDailyReport::getReportDate, query.getStartDate(), query.getEndDate());
        wrapper.like(StringUtils.isNotEmpty(query.getUserName()),
                WkDailyReport::getUserName, query.getUserName());
        wrapper.like(StringUtils.isNotEmpty(query.getDeptName()),
                WkDailyReport::getDeptName, query.getDeptName());
        wrapper.like(StringUtils.isNotEmpty(query.getProjectName()),
                WkDailyReport::getProjectName, query.getProjectName());
        wrapper.in(query.getUserIds() != null && !query.getUserIds().isEmpty(),
                WkDailyReport::getUserId, query.getUserIds());
        wrapper.in(query.getDeptIds() != null && !query.getDeptIds().isEmpty(),
                WkDailyReport::getDeptId, query.getDeptIds());
        wrapper.in(query.getProjectIds() != null && !query.getProjectIds().isEmpty(),
                WkDailyReport::getProjectId, query.getProjectIds());
        wrapper.orderByDesc(WkDailyReport::getReportDate, WkDailyReport::getUploadTime);

        List<WkDailyReport> list = wkDailyReportMapper.selectList(wrapper);

        return list.stream().map(this::convertToDetailVo).collect(Collectors.toList());
    }

    @Override
    public List<WkDailyReport> exportDailyReport(DailyReportQuery query) {
        LambdaQueryWrapper<WkDailyReport> wrapper = new LambdaQueryWrapper<>();
        wrapper.between(query.getStartDate() != null && query.getEndDate() != null,
                WkDailyReport::getReportDate, query.getStartDate(), query.getEndDate());

        if (query.getReportType() == 1) {
            // 人员维度
            wrapper.like(StringUtils.isNotEmpty(query.getUserName()),
                    WkDailyReport::getUserName, query.getUserName());
            wrapper.like(StringUtils.isNotEmpty(query.getProjectName()),
                    WkDailyReport::getProjectName, query.getProjectName());
            wrapper.in(query.getUserIds() != null && !query.getUserIds().isEmpty(),
                    WkDailyReport::getUserId, query.getUserIds());
            wrapper.in(query.getProjectIds() != null && !query.getProjectIds().isEmpty(),
                    WkDailyReport::getProjectId, query.getProjectIds());
            wrapper.orderByDesc(WkDailyReport::getReportDate, WkDailyReport::getUserName);

            List<WkDailyReport> list = wkDailyReportMapper.selectList(wrapper);
            list.forEach(this::convertAndCalculateReport);
            return list;
        } else if (query.getReportType() == 2) {
            // 部门维度
            return selectDeptDailyReport(query).getList();
        } else if (query.getReportType() == 3) {
            // 项目维度
            return selectProjectDailyReport(query).getList();
        }

        return new ArrayList<>();
    }

    /**
     * 转换时长单位并计算绩效点
     */
    private void convertAndCalculateReport(WkDailyReport report) {
        // 转换时长从秒到小时（保留2位小数）
        if (report.getEffectiveHours() != null) {
            BigDecimal effectiveHours = BigDecimal.valueOf(report.getEffectiveHours())
                    .divide(BigDecimal.valueOf(3600), 2, RoundingMode.HALF_UP);
            report.setEffectiveHours(effectiveHours.longValue());
        }

        if (report.getIneffectiveHours() != null) {
            BigDecimal ineffectiveHours = BigDecimal.valueOf(report.getIneffectiveHours())
                    .divide(BigDecimal.valueOf(3600), 2, RoundingMode.HALF_UP);
            report.setIneffectiveHours(ineffectiveHours.longValue());
        }

        if (report.getWorkHours() != null) {
            BigDecimal workHours = BigDecimal.valueOf(report.getWorkHours())
                    .divide(BigDecimal.valueOf(3600), 2, RoundingMode.HALF_UP);
            report.setWorkHours(workHours.longValue());
        }

        if (report.getAvgIntervalMinutes() != null) {
            BigDecimal intervalHours = BigDecimal.valueOf(report.getAvgIntervalMinutes())
                    .divide(BigDecimal.valueOf(3600), 2, RoundingMode.HALF_UP);
            report.setAvgIntervalMinutes(intervalHours.longValue());
        }

        // 计算绩效点：绩效点 = 合格指标数量 * 绩效系数
        if (report.getQualifiedCount() != null && report.getProjectId() != null) {
            BigDecimal coefficient = performanceCoefficientMapper.selectCoefficientByProjectId(report.getProjectId());
            if (coefficient == null) {
                coefficient = BigDecimal.ONE; // 默认系数为1
            }
            report.setPerformanceCoefficient(coefficient);

            BigDecimal performancePoints = BigDecimal.valueOf(report.getQualifiedCount())
                    .multiply(coefficient);
            report.setPerformancePoints(performancePoints);
        }
    }

    /**
     * 聚合部门维度报告
     */
    private WkDailyReport aggregateDeptReports(List<WkDailyReport> reports) {
        if (reports.isEmpty()) {
            return new WkDailyReport();
        }

        WkDailyReport first = reports.get(0);
        WkDailyReport aggregated = new WkDailyReport();
        aggregated.setReportDate(first.getReportDate());
        aggregated.setDeptId(first.getDeptId());
        aggregated.setDeptName(first.getDeptName());
        aggregated.setProjectId(first.getProjectId());
        aggregated.setProjectName(first.getProjectName());

        // 聚合数值字段
        long totalEffectiveHours = reports.stream().mapToLong(r -> r.getEffectiveHours() != null ? r.getEffectiveHours() : 0).sum();
        long totalIneffectiveHours = reports.stream().mapToLong(r -> r.getIneffectiveHours() != null ? r.getIneffectiveHours() : 0).sum();
        long totalWorkHours = reports.stream().mapToLong(r -> r.getWorkHours() != null ? r.getWorkHours() : 0).sum();
        long totalAvgInterval = reports.stream().mapToLong(r -> r.getAvgIntervalMinutes() != null ? r.getAvgIntervalMinutes() : 0).sum() / reports.size();
        long totalUnqualified = reports.stream().mapToLong(r -> r.getUnqualifiedCount() != null ? r.getUnqualifiedCount() : 0).sum();
        long totalQualified = reports.stream().mapToLong(r -> r.getQualifiedCount() != null ? r.getQualifiedCount() : 0).sum();
        long totalIndicators = reports.stream().mapToLong(r -> r.getTotalIndicators() != null ? r.getTotalIndicators() : 0).sum();
        long totalWorkOrders = reports.size(); // 工单数就是记录数

        BigDecimal totalPerformancePoints = reports.stream()
                .map(r -> r.getPerformancePoints() != null ? r.getPerformancePoints() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalEffectiveKm = reports.stream()
                .map(r -> r.getEffectiveKilometers() != null ? r.getEffectiveKilometers() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalIneffectiveKm = reports.stream()
                .map(r -> r.getIneffectiveKilometers() != null ? r.getIneffectiveKilometers() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalKm = reports.stream()
                .map(r -> r.getTotalKilometers() != null ? r.getTotalKilometers() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        aggregated.setEffectiveHours(totalEffectiveHours);
        aggregated.setIneffectiveHours(totalIneffectiveHours);
        aggregated.setWorkHours(totalWorkHours);
        aggregated.setAvgIntervalMinutes(totalAvgInterval);
        aggregated.setUnqualifiedCount(totalUnqualified);
        aggregated.setQualifiedCount(totalQualified);
        aggregated.setTotalIndicators(totalIndicators);
        aggregated.setWorkOrderCount(totalWorkOrders);
        aggregated.setPerformancePoints(totalPerformancePoints);
        aggregated.setEffectiveKilometers(totalEffectiveKm);
        aggregated.setIneffectiveKilometers(totalIneffectiveKm);
        aggregated.setTotalKilometers(totalKm);

        // 转换时长单位
        convertAndCalculateReport(aggregated);

        return aggregated;
    }

    /**
     * 聚合项目维度报告
     */
    private WkDailyReport aggregateProjectReports(List<WkDailyReport> reports) {
        if (reports.isEmpty()) {
            return new WkDailyReport();
        }

        WkDailyReport first = reports.get(0);
        WkDailyReport aggregated = new WkDailyReport();
        aggregated.setReportDate(first.getReportDate());
        aggregated.setProjectId(first.getProjectId());
        aggregated.setProjectName(first.getProjectName());

        // 聚合数值字段
        long totalEffectiveHours = reports.stream().mapToLong(r -> r.getEffectiveHours() != null ? r.getEffectiveHours() : 0).sum();
        long totalIneffectiveHours = reports.stream().mapToLong(r -> r.getIneffectiveHours() != null ? r.getIneffectiveHours() : 0).sum();
        long totalUnqualified = reports.stream().mapToLong(r -> r.getUnqualifiedCount() != null ? r.getUnqualifiedCount() : 0).sum();
        long totalQualified = reports.stream().mapToLong(r -> r.getQualifiedCount() != null ? r.getQualifiedCount() : 0).sum();
        long totalIndicators = reports.stream().mapToLong(r -> r.getTotalIndicators() != null ? r.getTotalIndicators() : 0).sum();
        long totalWorkOrders = reports.size(); // 当日完成工单数

        BigDecimal totalPerformancePoints = reports.stream()
                .map(r -> r.getPerformancePoints() != null ? r.getPerformancePoints() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalEffectiveKm = reports.stream()
                .map(r -> r.getEffectiveKilometers() != null ? r.getEffectiveKilometers() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalIneffectiveKm = reports.stream()
                .map(r -> r.getIneffectiveKilometers() != null ? r.getIneffectiveKilometers() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalKm = reports.stream()
                .map(r -> r.getTotalKilometers() != null ? r.getTotalKilometers() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        aggregated.setEffectiveHours(totalEffectiveHours);
        aggregated.setIneffectiveHours(totalIneffectiveHours);
        aggregated.setUnqualifiedCount(totalUnqualified);
        aggregated.setQualifiedCount(totalQualified);
        aggregated.setTotalIndicators(totalIndicators);
        aggregated.setWorkOrderCount(totalWorkOrders);
        aggregated.setPerformancePoints(totalPerformancePoints);
        aggregated.setEffectiveKilometers(totalEffectiveKm);
        aggregated.setIneffectiveKilometers(totalIneffectiveKm);
        aggregated.setTotalKilometers(totalKm);

        // 转换时长单位
        convertAndCalculateReport(aggregated);

        return aggregated;
    }

    /**
     * 转换为详细数据VO
     */
    private DailyReportDetailVo convertToDetailVo(WkDailyReport report) {
        DailyReportDetailVo vo = new DailyReportDetailVo();

        // 基本信息
        vo.setUserId(report.getUserId());
        vo.setUserName(report.getUserName());
        vo.setDeptId(report.getDeptId());
        vo.setDeptName(report.getDeptName());
        vo.setProjectId(report.getProjectId());
        vo.setProjectName(report.getProjectName());

        // 时间信息
        vo.setStartTime(report.getSurveyStartTime());
        vo.setEndTime(report.getSurveyEndTime());
        vo.setUploadTime(report.getUploadTime());

        // 时长信息（原始秒数）
        vo.setWorkDurationSeconds(report.getWorkHours());
        vo.setEffectiveDurationSeconds(report.getEffectiveHours());
        vo.setIneffectiveDurationSeconds(report.getIneffectiveHours());
        vo.setIntervalSeconds(report.getAvgIntervalMinutes());

        // 时长信息（转换为小时）
        if (report.getWorkHours() != null) {
            vo.setWorkDurationHours(BigDecimal.valueOf(report.getWorkHours())
                    .divide(BigDecimal.valueOf(3600), 2, RoundingMode.HALF_UP));
        }
        if (report.getEffectiveHours() != null) {
            vo.setEffectiveDurationHours(BigDecimal.valueOf(report.getEffectiveHours())
                    .divide(BigDecimal.valueOf(3600), 2, RoundingMode.HALF_UP));
        }
        if (report.getIneffectiveHours() != null) {
            vo.setIneffectiveDurationHours(BigDecimal.valueOf(report.getIneffectiveHours())
                    .divide(BigDecimal.valueOf(3600), 2, RoundingMode.HALF_UP));
        }
        if (report.getAvgIntervalMinutes() != null) {
            vo.setIntervalHours(BigDecimal.valueOf(report.getAvgIntervalMinutes())
                    .divide(BigDecimal.valueOf(3600), 2, RoundingMode.HALF_UP));
        }

        // 指标信息
        vo.setQualifiedCount(report.getQualifiedCount());
        vo.setUnqualifiedCount(report.getUnqualifiedCount());
        vo.setTotalIndicators(report.getTotalIndicators());

        // 绩效和公里数信息
        vo.setPerformancePoints(report.getPerformancePoints());
        vo.setPerformanceCoefficient(report.getPerformanceCoefficient());
        vo.setEffectiveKilometers(report.getEffectiveKilometers());
        vo.setIneffectiveKilometers(report.getIneffectiveKilometers());
        vo.setTotalKilometers(report.getTotalKilometers());

        return vo;
    }
}
