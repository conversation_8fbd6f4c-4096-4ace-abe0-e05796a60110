package com.xz.worker.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.xz.worker.domain.WkWorkCurrent;
import com.xz.worker.domain.mongo.UserTrackInfo;
import com.xz.worker.mapper.WkWorkCurrentMapper;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import com.xz.worker.mapper.WkWorkDetailMapper;
import com.xz.worker.domain.WkWorkDetail;
import com.xz.worker.service.IWkWorkDetailService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 工作轨迹副Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-01-29
 */
@Service
public class WkWorkDetailServiceImpl implements IWkWorkDetailService 
{
    @Autowired
    private WkWorkDetailMapper wkWorkDetailMapper;

    @Autowired
    private WkWorkCurrentMapper wkWorkCurrentMapper;

//    @Autowired
//    @Qualifier("primaryMongoTemplate")
//    private MongoTemplate mongoTemplate;



    /**
     * 查询工作轨迹副
     * 
     * @param workDetailId 工作轨迹副主键
     * @return 工作轨迹副
     */
    @Override
    public WkWorkDetail selectWkWorkDetailByWorkDetailId(Long workDetailId)
    {
        return wkWorkDetailMapper.selectWkWorkDetailByWorkDetailId(workDetailId);
    }

    @Override
    public List<WkWorkDetail> getTaskWorkdetails(WkWorkDetail wkWorkDetail) {
        return wkWorkDetailMapper.selectWkWorkDetailList(wkWorkDetail);
    }

    @Override
    public List<WkWorkDetail> selectWkWorkDetailListToAdmin(WkWorkDetail wkWorkDetail) {
        List<WkWorkDetail> res = new ArrayList<>();
        if (wkWorkDetail.getMonth() != null) {
            wkWorkDetail.setMonth("wk_work_detail_" + wkWorkDetail.getMonth());
        } else {
            wkWorkDetail.setMonth("wk_work_detail");
        }

        res = wkWorkDetailMapper.selectWkWorkDetailListToAdmin(wkWorkDetail);
        return res;
    }

    @Override
    public List<UserTrackInfo> selectWkWorkDetailListToAdminOnMongo(WkWorkDetail wkWorkDetail) {
//        Query query = new Query();
//        query.addCriteria(Criteria.where("taskId").is(wkWorkDetail.getTaskId()));
//        return mongoTemplate.find(query, UserTrackInfo.class, UserTrackInfo.COLLECTION_NAME + wkWorkDetail.getMonth());
        return new ArrayList<>();
    }

    /**
     * 查询工作轨迹副列表
     * 
     * @param wkWorkDetail 工作轨迹副
     * @return 工作轨迹副
     */
    @Override
    public List<WkWorkDetail> selectWkWorkDetailList(WkWorkDetail wkWorkDetail)
    {
        return wkWorkDetailMapper.selectWkWorkDetailList(wkWorkDetail);
    }

    @Override
    public List<WkWorkDetail> selectWkWorkDetailListV1(WkWorkDetail wkWorkDetail) {
        return wkWorkDetailMapper.selectWkWorkDetailListV1(wkWorkDetail);
    }

    /**
     * 新增工作轨迹副
     * 
     * @param wkWorkDetail 工作轨迹副
     * @return 结果
     */
    @Override
    @Transactional
    public int insertWkWorkDetail(WkWorkDetail wkWorkDetail)
    {
        // 更新当前位置
        WkWorkCurrent wkWorkCurrent = new WkWorkCurrent();
        wkWorkCurrent.setUserId(SecurityUtils.getUserId());
        if (wkWorkCurrentMapper.selectWkWorkCurrentList(wkWorkCurrent).size() > 0) {
            wkWorkCurrent.setLat(wkWorkDetail.getLat());
            wkWorkCurrent.setLon(wkWorkDetail.getLon());
            wkWorkCurrent.setCreateTime(new Date());
            wkWorkCurrentMapper.updateWkWorkCurrentByUserId(wkWorkCurrent);
        } else {
            wkWorkCurrent.setLat(wkWorkDetail.getLat());
            wkWorkCurrent.setLon(wkWorkDetail.getLon());
            wkWorkCurrent.setCreateTime(new Date());
            wkWorkCurrentMapper.insertWkWorkCurrent(wkWorkCurrent);
        }
        wkWorkDetail.setCreateTime(DateUtils.getNowDate());
        wkWorkDetail.setCalFlg(0);
        return wkWorkDetailMapper.insertWkWorkDetail(wkWorkDetail);
    }

    @Override
    public int insertList(List<WkWorkDetail> list) {
        WkWorkDetail wkWorkDetail = new WkWorkDetail();
        wkWorkDetail.setList(list);
        wkWorkDetail.setMonth("wk_work_detail_" + DateUtils.parseDateToStr("yyyyMM", new Date()));
        return wkWorkDetailMapper.insertList(wkWorkDetail);
    }

    /**
     * 修改工作轨迹副
     * 
     * @param wkWorkDetail 工作轨迹副
     * @return 结果
     */
    @Override
    public int updateWkWorkDetail(WkWorkDetail wkWorkDetail)
    {
        wkWorkDetail.setUpdateTime(DateUtils.getNowDate());
        return wkWorkDetailMapper.updateWkWorkDetail(wkWorkDetail);
    }

    /**
     * 批量删除工作轨迹副
     * 
     * @param workDetailIds 需要删除的工作轨迹副主键
     * @return 结果
     */
    @Override
    public int deleteWkWorkDetailByWorkDetailIds(Long[] workDetailIds)
    {
        return wkWorkDetailMapper.deleteWkWorkDetailByWorkDetailIds(workDetailIds);
    }

    /**
     * 删除工作轨迹副信息
     * 
     * @param workDetailId 工作轨迹副主键
     * @return 结果
     */
    @Override
    public int deleteWkWorkDetailByWorkDetailId(Long workDetailId)
    {
        return wkWorkDetailMapper.deleteWkWorkDetailByWorkDetailId(workDetailId);
    }

    @Override
    public List<WkWorkDetail> getWorkorderTracks(Long userId) {
        return wkWorkDetailMapper.getWorkorderTracks(userId, "wk_work_detail_" + DateUtils.parseDateToStr("yyyyMM", new Date()));
    }

    @Override
    public List<WkWorkDetail> getWorkOrderTracksByTaskIds(List<Long> taskIdList) {

        String tableName = "wk_work_detail_" + DateUtils.parseDateToStr("yyyyMM", new Date());
        String taskIds = taskIdList.stream().map(String::valueOf).collect(Collectors.joining(","));
        return wkWorkDetailMapper.getWorkorderTracksByTaskIds(tableName, taskIds);
    }
}
