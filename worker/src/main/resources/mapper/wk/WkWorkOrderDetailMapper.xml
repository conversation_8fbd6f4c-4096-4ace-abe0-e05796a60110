<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xz.worker.mapper.WkWorkOrderDetailMapper">
    
    <resultMap type="WkWorkOrderDetail" id="WkWorkOrderDetailResult">
        <result property="orderDetailId"    column="order_detail_id"    />
        <result property="workOrderId"    column="work_order_id"    />
        <result property="userId"    column="user_id"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="processUrl"    column="process_url"    />
        <result property="pressdoStatus"    column="pressdo_status"    />
        <result property="remark"    column="remark"    />
        <result property="remarkType"    column="remark_type"    />
        <result property="type"    column="type"    />
        <result property="appealCount"    column="appeal_count"    />
        <result property="ext"    column="ext"    />
        <result property="nickName"    column="nick_name"    />
    </resultMap>

    <resultMap type="WkCommon" id="WkCommonResult">
        <result property="userId"    column="user_id"    />
        <result property="num"    column="num"    />
    </resultMap>

    <resultMap type="TimeOutWorkOrder" id="WkTimeOutWorkOrderResult">
        <result property="userId"    column="userId"    />
        <result property="cityIndex"    column="cityIndex"    />
        <result property="closeTime"    column="closeTime"    />
        <result property="startTime"    column="startTime"    />
        <result property="workOrderId"    column="workOrderId"    />
        <result property="status"    column="status"    />
        <result property="address"    column="address"    />
        <result property="pictureUrl"    column="pictureUrl"    />
        <result property="gridId"    column="gridId"    />
    </resultMap>

    <resultMap type="WorkorderDeptResult" id="workorderDeptResult">
        <result property="deptId"    column="deptId"    />
        <result property="deptName"    column="deptName"    />
        <result property="taskStatus"    column="taskStatus"    />
        <result property="num"    column="num"    />
    </resultMap>

    <resultMap type="WorkorderGridResult" id="workorderGridResult">
        <result property="gridId"    column="gridId"    />
        <result property="gridName"    column="gridName"    />
        <result property="taskStatus"    column="taskStatus"    />
        <result property="num"    column="num"    />
    </resultMap>

    <resultMap type="WorkorderResult" id="workorderResult">
        <result property="title"    column="title"    />
        <result property="taskStatus"    column="taskStatus"    />
        <result property="num"    column="num"    />
    </resultMap>

    <resultMap type="WorkTotalResult" id="workTotalResult">
        <result property="name"    column="name"    />
        <result property="id"    column="id"    />
        <result property="count"    column="count"    />
    </resultMap>

    <sql id="selectWkWorkOrderDetailVo">
        select order_detail_id, work_order_id, user_id, status, del_flag, create_by, create_time, update_by, update_time, tenant_id,process_url,pressdo_status,remark,remark_type,appeal_count from wk_work_order_detail
    </sql>

    <select id="selectWkWorkOrderDetailList" parameterType="WkWorkOrderDetail" resultMap="WkWorkOrderDetailResult">
        <include refid="selectWkWorkOrderDetailVo"/>
        <where>  
            <if test="workOrderId != null "> and work_order_id = #{workOrderId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="tenantId != null "> and tenant_id = #{tenantId}</if>
        </where>
    </select>

    <select id="getWkWorkOrderDetails" parameterType="Long" resultMap="WkWorkOrderDetailResult">
        SELECT
        wod.* ,
        su.nick_name
        FROM
        wk_work_order_detail wod left join sys_user su on su.user_id = wod.user_id
        <where>
            <if test="workOrderId != null "> and wod.work_order_id = #{workOrderId}</if>
             and wod.status not in (6,7,9)
             order by wod.create_time desc,wod.status asc
        </where>
    </select>

    <select id="getClabelsByParents" parameterType="Long" resultType="Long">
      SELECT
	    clabel_id
        FROM
	    wk_city_label
        WHERE
	    parent_id IN (
	        SELECT
		    clabel_id
	    FROM
		    wk_city_label
         WHERE
	        parent_id = #{parentId})
    </select>

    <resultMap id="cityLabelsToreportMap" type="com.xz.worker.domain.CityLabelsToreport">
        <result property="clabelId"    column="clabel_id"    />
        <result property="name"    column="name"    />
        <result property="level1Id"    column="level1Id"    />
        <result property="level1Name"    column="level1Name"    />
    </resultMap>

    <select id="getCityLabelsToreport" resultMap="cityLabelsToreportMap">
        SELECT
            l1.clabel_id,
            l1.NAME,
        IF
            ( l3.clabel_id IS NULL, l2.clabel_id, l3.clabel_id ) AS level1Id,
        IF
            ( l3.clabel_id IS NULL, l2.NAME, l3.NAME ) AS level1Name
        FROM
            wk_city_label l1
            LEFT JOIN wk_city_label l2 ON l1.parent_id = l2.clabel_id
            LEFT JOIN wk_city_label l3 ON l2.parent_id = l3.clabel_id
        WHERE
            l1.parent_id != 0
            AND l1.type = 1
    </select>

    <select id="getSubClabelsByParents" parameterType="Long" resultType="Long">
         SELECT
		    clabel_id
	     FROM
		    wk_city_label
         WHERE
	        parent_id = #{parentId}
    </select>

    <select id="getWorkOrdersByGrid" parameterType="Long" resultMap="workTotalResult">
     SELECT
	wg.grid_id as id,
	IFNULL( t.count, 0 ) AS count,
	wg.`name`,
	wg.lon,
	wg.lat
FROM
	wk_grid wg
	LEFT JOIN (
	SELECT
		count( 1 ) AS count,
		city_grid AS id
	FROM
		wk_work_order wo
	WHERE
		wo.city_grid IN ( SELECT grid_id FROM wk_grid WHERE parent_id = #{parentId} )
	GROUP BY
		city_grid
	) t ON t.id = wg.grid_id
WHERE
	wg.parent_id =#{parentId}
    </select>

    <select id="selectWkWorkOrderDetailListOnTimeout" parameterType="WkWorkOrderDetail" resultMap="WkWorkOrderDetailResult">
        <include refid="selectWkWorkOrderDetailVo"/>
        <where>
            <if test="workOrderId != null "> and work_order_id = #{workOrderId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
             and status in (6,7)
        </where>
    </select>

    <select id="findWillTimeOutWorkOrder" resultMap="WkTimeOutWorkOrderResult">
      SELECT
        wod.user_id AS userId,
        wo.city_index AS cityIndex,
        wo.close_time AS closeTime,
        wod.create_time as startTime,
				wo.address,
				wo.picture_url as pictureUrl,
				wo.city_grid as gridId,
        wod.work_order_id as workOrderId,
        wod.order_detail_id as orderDetailId,
        wod.status
        FROM
        wk_work_order_detail wod
        LEFT JOIN wk_work_order wo ON wo.work_order_id = wod.work_order_id
        WHERE
        wod.`status` = 6
    </select>

    <select id="findWillTimeOutWorkOrderUser" resultMap="WkTimeOutWorkOrderResult">
        SELECT
        wo.user_id AS userId,
        wo.city_index AS cityIndex,
        wo.close_time AS closeTime,
        wo.create_time as startTime,
				wo.address,
				wo.picture_url as pictureUrl,
				wo.city_grid as gridId,
        wo.work_order_id as workOrderId,
        wo.status
        FROM
        wk_work_order wo
        WHERE
        wo.`status` = 0
    </select>

    <select id="findMinWorksUserId" parameterType="java.util.List" resultMap="WkCommonResult">
        SELECT
        count( 1 ) AS num,
        user_id
        FROM
        wk_work_order
        <where>
            and `status` = 0 and user_id in
            <foreach item="userIds" collection="list" open="(" separator="," close=")">
                #{userIds}
            </foreach>
        </where>
        GROUP BY
        user_id
        ORDER BY
        num
    </select>
    
    <select id="selectWkWorkOrderDetailByOrderDetailId" parameterType="Long" resultMap="WkWorkOrderDetailResult">
        <include refid="selectWkWorkOrderDetailVo"/>
        where order_detail_id = #{orderDetailId}
    </select>

    <select id="getWorkorderDeptNumbers" resultMap="workorderDeptResult" parameterType="com.xz.worker.domain.vo.QueryBigScreen">
    SELECT
	su.dept_id AS deptId,
	sd.dept_name as deptName,
	wo.`status` AS taskStatus,
	count(*) num
    FROM
    wk_work_order wo
    INNER JOIN sys_user su ON wo.user_id = su.user_id
    inner JOIN sys_dept sd ON su.dept_id = sd.dept_id
        <where>
          <if test="street != null">
              and wo.city_grid = #{street}
          </if>
            <if test="type != null">
                and wo.type = #{type}
            </if>
            <if test="addresss!= null and addresss.size() > 0">
                <foreach collection="addresss" item="item" open=" and wo.address in (" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="indexs!= null and indexs.size() > 0">
                <foreach collection="indexs" item="item" open=" and wo.city_index in (" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="status != null and status == 1">
                and wo.status = 1
            </if>
            <if test="status != null and status == 3">
                and wo.status = 3
            </if>
            <if test="status != null and status != 1 and status != 3">
                and wo.status not in (1,3)
            </if>
            <if test="startTime != null"><!-- 开始时间检索 -->
                and date_format(wo.create_time,'%Y-%m-%d %H:%i:%S') &gt;= date_format(#{startTime},'%Y-%m-%d %H:%i:%S')
            </if>
            <if test="endTime != null"><!-- 结束时间检索 -->
                and date_format(wo.create_time,'%Y-%m-%d %H:%i:%S') &lt;= date_format(#{endTime},'%Y-%m-%d %H:%i:%S')
            </if>
            <if test="dept != null">
                and sd.dept_id = #{dept}
            </if>
        </where>
    GROUP BY
	deptId,
	taskStatus
    </select>

    <select id="getWorkorderGrid4HourNumbers" resultMap="workorderGridResult">
    SELECT
	wo.city_grid AS gridId,
	wg.`name` AS gridName,
	wo.`status` AS taskStatus,
	count(*) num
    FROM
	wk_work_order wo
	LEFT JOIN wk_grid wg ON wg.grid_id = wo.city_grid
    WHERE
        date_format( wo.create_time, '%Y-%m-%d %H' ) BETWEEN date_format( date_sub( now(), INTERVAL 4 HOUR ), '%Y-%m-%d %H' )
        AND date_format( now(), '%Y-%m-%d %H' )
    GROUP BY
	gridId,
	taskStatus
    </select>

    <select id="getWorkorderGridDayNumbers" resultMap="workorderGridResult">
    SELECT
	wo.city_grid AS gridId,
	wg.`name` AS gridName,
	wo.`status` AS taskStatus,
	count(*) num
    FROM
	wk_work_order wo
	LEFT JOIN wk_grid wg ON wg.grid_id = wo.city_grid
    WHERE
        date_format(wo.create_time, '%Y-%m-%d') = date_format(now(), '%Y-%m-%d')
    GROUP BY
	gridId,
	taskStatus
    </select>

    <select id="getWorkorderGridMonthNumbers" resultMap="workorderGridResult">
        SELECT
        wo.city_grid AS gridId,
        wg.`name` AS gridName,
        wo.`status` AS taskStatus,
        count(*) num
        FROM
        wk_work_order wo
        LEFT JOIN wk_grid wg ON wg.grid_id = wo.city_grid
        WHERE
            date_format(wo.create_time, '%Y-%m') = date_format(now(), '%Y-%m')
        GROUP BY
        gridId,
        taskStatus
    </select>

    <select id="getWorkorderMonthNumbers" resultMap="workorderResult" parameterType="com.xz.worker.domain.vo.QueryBigScreen">
        SELECT
        date_format( wo.create_time, '%Y-%m-%d' ) AS title,
        wo.`status` AS taskStatus,
        count(*) num
        FROM
        wk_work_order wo
        inner join sys_user su on su.user_id = wo.user_id
        <where>
            <if test="street!= null">
                and wo.city_grid = #{street}
            </if>
            <if test="dept!= null">
                and su.dept_id = #{dept}
            </if>
            <if test="addresss!= null and addresss.size() > 0">
                <foreach collection="addresss" item="item" open=" and wo.address in (" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="indexs!= null and indexs.size() > 0">
                <foreach collection="indexs" item="item" open=" and wo.city_index in (" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="status != null and status == 1">
                and wo.status = 1
            </if>
            <if test="status != null and status == 3">
                and wo.status = 3
            </if>
            <if test="status != null and status != 1 and status != 3">
                and wo.status not in (1,3)
            </if>
            and date_format(wo.create_time, '%Y-%m') = date_format(now(), '%Y-%m')
        </where>
        GROUP BY
        date_format(
        wo.create_time,
        '%Y-%m-%d'),taskStatus
    </select>

    <select id="getWorkorderDayNumbers" resultMap="workorderResult" parameterType="com.xz.worker.domain.vo.QueryBigScreen">
        SELECT
        date_format( wo.create_time, '%Y-%m-%d' ) AS title,
        wo.`status` AS taskStatus,
        count(*) num
        FROM
        wk_work_order wo
        inner join sys_user su on su.user_id = wo.user_id
        <where>
            <if test="street!= null">
                and wo.city_grid = #{street}
            </if>
            <if test="dept!= null">
                and su.dept_id = #{dept}
            </if>
            <if test="addresss!= null and addresss.size() > 0">
                <foreach collection="addresss" item="item" open=" and wo.address in (" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="indexs!= null and indexs.size() > 0">
                <foreach collection="indexs" item="item" open=" and wo.city_index in (" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="status != null and status == 1">
                and wo.status = 1
            </if>
            <if test="status != null and status == 3">
                and wo.status = 3
            </if>
            <if test="status != null and status != 1 and status != 3">
                and wo.status not in (1,3)
            </if>
            and YEARWEEK(wo.create_time, 1) = YEARWEEK(NOW(), 1)
        </where>
        GROUP BY
        date_format(
        wo.create_time,
        '%Y-%m-%d'),taskStatus
    </select>

    <select id="getWorkorderHourNumbers" resultMap="workorderResult" parameterType="com.xz.worker.domain.vo.QueryBigScreen">
        SELECT
        date_format( wo.create_time, '%Y-%m-%d %H' ) AS title,
        wo.`status` AS taskStatus,
        count(*) num
        FROM
        wk_work_order wo
        inner join sys_user su on su.user_id = wo.user_id
        <where>
            <if test="street!= null">
                and wo.city_grid = #{street}
            </if>
            <if test="dept!= null">
                and su.dept_id = #{dept}
            </if>
            <if test="addresss!= null and addresss.size() > 0">
                <foreach collection="addresss" item="item" open=" and wo.address in (" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="indexs!= null and indexs.size() > 0">
                <foreach collection="indexs" item="item" open=" and wo.city_index in (" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="status != null and status == 1">
                and wo.status = 1
            </if>
            <if test="status != null and status == 3">
                and wo.status = 3
            </if>
            <if test="status != null and status != 1 and status != 3">
                and wo.status not in (1,3)
            </if>
            and date_format(wo.create_time, '%Y-%m-%d') = date_format(now(), '%Y-%m-%d')
        </where>
        GROUP BY
        date_format(
        wo.create_time,
        '%Y-%m-%d %H'),taskStatus
    </select>
        
    <insert id="insertWkWorkOrderDetail" parameterType="WkWorkOrderDetail" useGeneratedKeys="true" keyProperty="orderDetailId">
        insert into wk_work_order_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workOrderId != null">work_order_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="type != null">type,</if>
            <if test="remark != null">remark,</if>
            <if test="remarkType != null">remark_type,</if>
            <if test="appealCount != null">appeal_count,</if>
            <if test="ext != null">ext,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workOrderId != null">#{workOrderId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="type != null">#{type},</if>
            <if test="remark != null">#{remark},</if>
            <if test="remarkType != null">#{remarkType},</if>
            <if test="appealCount != null">#{appealCount},</if>
            <if test="ext != null">#{ext},</if>
         </trim>
    </insert>

    <insert id="insertList" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="orderDetailId">
        insert into wk_work_order_detail (work_order_id, user_id, status,
        del_flag, create_by, create_time,
        update_by, update_time, tenant_id,type,remark_type,appeal_count
        )
        values
        <foreach collection="list" item="item" separator="," >
            (#{item.workOrderId},#{item.userId},#{item.status},#{item.delFlag},#{item.createBy},#{item.createTime},#{item.updateBy},#{item.updateTime},#{item.tenantId},#{item.type},#{item.remarkType},#{item.appealCount})
        </foreach>
    </insert>

    <update id="updateWkWorkOrderDetail" parameterType="WkWorkOrderDetail">
        update wk_work_order_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="workOrderId != null">work_order_id = #{workOrderId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="type != null">remark = #{type},</if>
            <if test="remarkType != null">remark_type = #{remarkType},</if>
            <if test="appealCount != null">appeal_count = #{appealCount},</if>
            <if test="processUrl != null">process_url = #{processUrl},</if>
            <if test="pressdoStatus != null">pressdo_status = #{pressdoStatus},</if>
            <if test="ext != null">ext = #{ext},</if>
        </trim>
        where order_detail_id = #{orderDetailId}
    </update>

    <delete id="deleteWkWorkOrderDetailByOrderDetailId" parameterType="Long">
        delete from wk_work_order_detail where order_detail_id = #{orderDetailId}
    </delete>

    <delete id="deleteWkWorkOrderDetailByOrderDetailIds" parameterType="String">
        delete from wk_work_order_detail where order_detail_id in 
        <foreach item="orderDetailId" collection="array" open="(" separator="," close=")">
            #{orderDetailId}
        </foreach>
    </delete>

    <delete id="deleteWkWorkOrderDetailsByOrderId" parameterType="Long">
        delete from wk_work_order_detail where work_order_id = #{workOrderId} and status in (6,7)
    </delete>
</mapper>