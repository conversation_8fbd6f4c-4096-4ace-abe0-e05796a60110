<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xz.worker.mapper.WkDailyReportMapper">
    <resultMap type="com.xz.worker.domain.WkDailyReport" id="WkDailyReportResult">
        <result property="id" column="id"/>
        <result property="reportDate" column="report_date"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="projectId" column="project_id"/>
        <result property="projectName" column="project_name"/>
        <result property="surveyStartTime" column="survey_start_time"/>
        <result property="surveyEndTime" column="survey_end_time"/>
        <result property="effectiveHours" column="effective_hours"/>
        <result property="ineffectiveHours" column="ineffective_hours"/>
        <result property="workHours" column="work_hours"/>
        <result property="avgIntervalMinutes" column="avg_interval_minutes"/>
        <result property="unqualifiedCount" column="unqualified_count"/>
        <result property="qualifiedCount" column="qualified_count"/>
        <result property="totalIndicators" column="total_indicators"/>
        <result property="workOrderCount" column="work_order_count"/>
        <result property="performancePoints" column="performance_points"/>
        <result property="effectiveKilometers" column="effective_kilometers"/>
        <result property="ineffectiveKilometers" column="ineffective_kilometers"/>
        <result property="totalKilometers" column="total_kilometers"/>
        <result property="uploadTime" column="upload_time"/>
        <result property="performanceCoefficient" column="performance_coefficient"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
</mapper>